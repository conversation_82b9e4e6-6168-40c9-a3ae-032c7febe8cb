/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(dashboard)/dashboard/page";
exports.ids = ["app/(dashboard)/dashboard/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(dashboard)%2Fdashboard%2Fpage&page=%2F(dashboard)%2Fdashboard%2Fpage&appPaths=%2F(dashboard)%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fdashboard%2Fpage.js&appDir=D%3A%5CProjects%5CCpa-dashboard%5Ccpa-dashboard%5Ccpa-dashboard%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5CCpa-dashboard%5Ccpa-dashboard%5Ccpa-dashboard%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(dashboard)%2Fdashboard%2Fpage&page=%2F(dashboard)%2Fdashboard%2Fpage&appPaths=%2F(dashboard)%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fdashboard%2Fpage.js&appDir=D%3A%5CProjects%5CCpa-dashboard%5Ccpa-dashboard%5Ccpa-dashboard%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5CCpa-dashboard%5Ccpa-dashboard%5Ccpa-dashboard%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"(rsc)/./node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/experimental/ppr */ \"(rsc)/./node_modules/next/dist/server/lib/experimental/ppr.js\");\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/request/fallback-params */ \"(rsc)/./node_modules/next/dist/server/request/fallback-params.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/app-render/encryption-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/encryption-utils.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/lib/streaming-metadata */ \"(rsc)/./node_modules/next/dist/server/lib/streaming-metadata.js\");\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/app-render/action-utils */ \"(rsc)/./node_modules/next/dist/server/app-render/action-utils.js\");\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/server/lib/server-action-request-meta */ \"(rsc)/./node_modules/next/dist/server/lib/server-action-request-meta.js\");\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/client/components/app-router-headers */ \"(rsc)/./node_modules/next/dist/client/components/app-router-headers.js\");\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/lib/fallback */ \"(rsc)/./node_modules/next/dist/lib/fallback.js\");\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/server/render-result */ \"(rsc)/./node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/server/stream-utils/encoded-tags */ \"(rsc)/./node_modules/next/dist/server/stream-utils/encoded-tags.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/server/send-payload */ \"(rsc)/./node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\");\n/* harmony import */ var next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"(rsc)/./node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\",\"handler\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.js */ \"(rsc)/./src/app/layout.js\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/not-found.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/not-found.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/forbidden.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/forbidden.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/unauthorized.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/unauthorized.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(dashboard)/layout.js */ \"(rsc)/./src/app/(dashboard)/layout.js\"));\nconst module6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/not-found.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/not-found.js\", 23));\nconst module7 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/forbidden.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/forbidden.js\", 23));\nconst module8 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/unauthorized.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/unauthorized.js\", 23));\nconst page9 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(dashboard)/dashboard/page.js */ \"(rsc)/./src/app/(dashboard)/dashboard/page.js\"));\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(dashboard)',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page9, \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\layout.js\"],\n'not-found': [module6, \"next/dist/client/components/builtin/not-found.js\"],\n'forbidden': [module7, \"next/dist/client/components/builtin/forbidden.js\"],\n'unauthorized': [module8, \"next/dist/client/components/builtin/unauthorized.js\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\layout.js\"],\n'global-error': [module1, \"next/dist/client/components/builtin/global-error.js\"],\n'not-found': [module2, \"next/dist/client/components/builtin/not-found.js\"],\n'forbidden': [module3, \"next/dist/client/components/builtin/forbidden.js\"],\n'unauthorized': [module4, \"next/dist/client/components/builtin/unauthorized.js\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\"];\n\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(dashboard)/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || ''\n});\nasync function handler(req, res, ctx) {\n    var _this;\n    let srcPage = \"/(dashboard)/dashboard/page\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const initialPostponed = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'postponed');\n    // TODO: replace with more specific flags\n    const minimalMode = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'minimalMode');\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, query, params, parsedUrl, pageIsDynamic, buildManifest, nextFontManifest, reactLoadableManifest, serverActionsManifest, clientReferenceManifest, subresourceIntegrityManifest, prerenderManifest, isDraftMode, resolvedPathname, revalidateOnlyGenerated, routerServerContext, nextConfig } = prepareResult;\n    const pathname = parsedUrl.pathname || '/';\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__.normalizeAppPath)(srcPage);\n    let { isOnDemandRevalidate } = prepareResult;\n    const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n    const isPrerendered = prerenderManifest.routes[resolvedPathname];\n    let isSSG = Boolean(prerenderInfo || isPrerendered || prerenderManifest.routes[normalizedSrcPage]);\n    const userAgent = req.headers['user-agent'] || '';\n    const botType = (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.getBotType)(userAgent);\n    const isHtmlBot = (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.isHtmlBotRequest)(req);\n    /**\n   * If true, this indicates that the request being made is for an app\n   * prefetch request.\n   */ const isPrefetchRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isPrefetchRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_ROUTER_PREFETCH_HEADER]);\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n    const isRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.RSC_HEADER]);\n    const isPossibleServerAction = (0,next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__.getIsPossibleServerAction)(req);\n    /**\n   * If the route being rendered is an app page, and the ppr feature has been\n   * enabled, then the given route _could_ support PPR.\n   */ const couldSupportPPR = (0,next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__.checkIsAppPPREnabled)(nextConfig.experimental.ppr);\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =  false && 0;\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery = hasDebugStaticShellQuery && query.__nextppronly === 'fallback';\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled = couldSupportPPR && (((_this = prerenderManifest.routes[normalizedSrcPage] ?? prerenderManifest.dynamicRoutes[normalizedSrcPage]) == null ? void 0 : _this.renderingMode) === 'PARTIALLY_STATIC' || // Ideally we'd want to check the appConfig to see if this page has PPR\n    // enabled or not, but that would require plumbing the appConfig through\n    // to the server during development. We assume that the page supports it\n    // but only during development.\n    hasDebugStaticShellQuery && (routeModule.isDev === true || (routerServerContext == null ? void 0 : routerServerContext.experimentalTestProxy) === true));\n    const isDebugStaticShell = hasDebugStaticShellQuery && isRoutePPREnabled;\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses = isDebugStaticShell && routeModule.isDev === true;\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled;\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled ? initialPostponed : undefined;\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest = isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest;\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'segmentPrefetchRSCRequest');\n    // TODO: investigate existing bug with shouldServeStreamingMetadata always\n    // being true for a revalidate due to modifying the base-server this.renderOpts\n    // when fixing this to correct logic it causes hydration issue since we set\n    // serveStreamingMetadata to true during export\n    let serveStreamingMetadata = !userAgent ? true : (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.shouldServeStreamingMetadata)(userAgent, nextConfig.htmlLimitedBots);\n    if (isHtmlBot && isRoutePPREnabled) {\n        isSSG = false;\n        serveStreamingMetadata = false;\n    }\n    // In development, we always want to generate dynamic HTML.\n    let supportsDynamicResponse = // If we're in development, we always support dynamic HTML, unless it's\n    // a data request, in which case we only produce static HTML.\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isSSG || // If this request has provided postponed data, it supports dynamic\n    // HTML.\n    typeof initialPostponed === 'string' || // If this is a dynamic RSC request, then this render supports dynamic\n    // HTML (it's dynamic).\n    isDynamicRSCRequest;\n    // When html bots request PPR page, perform the full dynamic rendering.\n    const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled;\n    let ssgCacheKey = null;\n    if (!isDraftMode && isSSG && !supportsDynamicResponse && !isPossibleServerAction && !minimalPostponed && !isDynamicRSCRequest) {\n        ssgCacheKey = resolvedPathname;\n    }\n    // the staticPathKey differs from ssgCacheKey since\n    // ssgCacheKey is null in dev since we're always in \"dynamic\"\n    // mode in dev to bypass the cache, but we still need to honor\n    // dynamicParams = false in dev mode\n    let staticPathKey = ssgCacheKey;\n    if (!staticPathKey && routeModule.isDev) {\n        staticPathKey = resolvedPathname;\n    }\n    const ComponentMod = {\n        ...next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__,\n        tree,\n        pages,\n        GlobalError: (next_dist_client_components_builtin_global_error_js__WEBPACK_IMPORTED_MODULE_24___default()),\n        handler,\n        routeModule,\n        __next_app__\n    };\n    // Before rendering (which initializes component tree modules), we have to\n    // set the reference manifests to our global store so Server Action's\n    // encryption util can access to them at the top level of the page module.\n    if (serverActionsManifest && clientReferenceManifest) {\n        (0,next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__.setReferenceManifestsSingleton)({\n            page: srcPage,\n            clientReferenceManifest,\n            serverActionsManifest,\n            serverModuleMap: (0,next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__.createServerModuleMap)({\n                serverActionsManifest\n            })\n        });\n    }\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const invokeRouteModule = async (span, context)=>{\n            const nextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextRequest(req);\n            const nextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextResponse(res);\n            // TODO: adapt for putting the RDC inside the postponed data\n            // If we're in dev, and this isn't a prefetch or a server action,\n            // we should seed the resume data cache.\n            if (true) {\n                if (nextConfig.experimental.dynamicIO && !isPrefetchRSCRequest && !context.renderOpts.isPossibleServerAction) {\n                    const warmup = await routeModule.warmup(nextReq, nextRes, context);\n                    // If the warmup is successful, we should use the resume data\n                    // cache from the warmup.\n                    if (warmup.metadata.renderResumeDataCache) {\n                        context.renderOpts.renderResumeDataCache = warmup.metadata.renderResumeDataCache;\n                    }\n                }\n            }\n            return routeModule.render(nextReq, nextRes, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const doRender = async ({ span, postponed, fallbackRouteParams })=>{\n            const context = {\n                query,\n                params,\n                page: normalizedSrcPage,\n                sharedContext: {\n                    buildId\n                },\n                serverComponentsHmrCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'serverComponentsHmrCache'),\n                fallbackRouteParams,\n                renderOpts: {\n                    App: ()=>null,\n                    Document: ()=>null,\n                    pageConfig: {},\n                    ComponentMod,\n                    Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(ComponentMod),\n                    params,\n                    routeModule,\n                    page: srcPage,\n                    postponed,\n                    shouldWaitOnAllReady,\n                    serveStreamingMetadata,\n                    supportsDynamicResponse: typeof postponed === 'string' || supportsDynamicResponse,\n                    buildManifest,\n                    nextFontManifest,\n                    reactLoadableManifest,\n                    subresourceIntegrityManifest,\n                    serverActionsManifest,\n                    clientReferenceManifest,\n                    setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                    dir: routeModule.projectDir,\n                    isDraftMode,\n                    isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n                    botType,\n                    isOnDemandRevalidate,\n                    isPossibleServerAction,\n                    assetPrefix: nextConfig.assetPrefix,\n                    nextConfigOutput: nextConfig.output,\n                    crossOrigin: nextConfig.crossOrigin,\n                    trailingSlash: nextConfig.trailingSlash,\n                    previewProps: prerenderManifest.preview,\n                    deploymentId: nextConfig.deploymentId,\n                    enableTainting: nextConfig.experimental.taint,\n                    htmlLimitedBots: nextConfig.htmlLimitedBots,\n                    devtoolSegmentExplorer: nextConfig.experimental.devtoolSegmentExplorer,\n                    reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n                    multiZoneDraftMode,\n                    incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'incrementalCache'),\n                    cacheLifeProfiles: nextConfig.experimental.cacheLife,\n                    basePath: nextConfig.basePath,\n                    serverActions: nextConfig.experimental.serverActions,\n                    ...isDebugStaticShell || isDebugDynamicAccesses ? {\n                        nextExport: true,\n                        supportsDynamicResponse: false,\n                        isStaticGeneration: true,\n                        isRevalidate: true,\n                        isDebugDynamicAccesses: isDebugDynamicAccesses\n                    } : {},\n                    experimental: {\n                        isRoutePPREnabled,\n                        expireTime: nextConfig.expireTime,\n                        staleTimes: nextConfig.experimental.staleTimes,\n                        dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                        clientSegmentCache: Boolean(nextConfig.experimental.clientSegmentCache),\n                        dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n                        inlineCss: Boolean(nextConfig.experimental.inlineCss),\n                        authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n                        clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                    },\n                    waitUntil: ctx.waitUntil,\n                    onClose: (cb)=>{\n                        res.on('close', cb);\n                    },\n                    onAfterTaskError: ()=>{},\n                    onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext),\n                    err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'invokeError'),\n                    dev: routeModule.isDev\n                }\n            };\n            const result = await invokeRouteModule(span, context);\n            const { metadata } = result;\n            const { cacheControl, headers = {}, // Add any fetch tags that were on the page to the response headers.\n            fetchTags: cacheTags } = metadata;\n            if (cacheTags) {\n                headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n            }\n            // Pull any fetch metrics from the render onto the request.\n            ;\n            req.fetchMetrics = metadata.fetchMetrics;\n            // we don't throw static to dynamic errors in dev as isSSG\n            // is a best guess in dev since we don't have the prerender pass\n            // to know whether the path is actually static or not\n            if (isSSG && (cacheControl == null ? void 0 : cacheControl.revalidate) === 0 && !routeModule.isDev && !isRoutePPREnabled) {\n                const staticBailoutInfo = metadata.staticBailoutInfo;\n                const err = Object.defineProperty(new Error(`Page changed from static to dynamic at runtime ${resolvedPathname}${(staticBailoutInfo == null ? void 0 : staticBailoutInfo.description) ? `, reason: ${staticBailoutInfo.description}` : ``}` + `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E132\",\n                    enumerable: false,\n                    configurable: true\n                });\n                if (staticBailoutInfo == null ? void 0 : staticBailoutInfo.stack) {\n                    const stack = staticBailoutInfo.stack;\n                    err.stack = err.message + stack.substring(stack.indexOf('\\n'));\n                }\n                throw err;\n            }\n            return {\n                value: {\n                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE,\n                    html: result,\n                    headers,\n                    rscData: metadata.flightData,\n                    postponed: metadata.postponed,\n                    status: metadata.statusCode,\n                    segmentData: metadata.segmentData\n                },\n                cacheControl\n            };\n        };\n        const responseGenerator = async ({ hasResolved, previousCacheEntry, isRevalidating, span })=>{\n            const isProduction = routeModule.isDev === false;\n            const didRespond = hasResolved || res.writableEnded;\n            // skip on-demand revalidate if cache is not present and\n            // revalidate-if-generated is set\n            if (isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry && !minimalMode) {\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res);\n                } else {\n                    res.statusCode = 404;\n                    res.end('This page could not be found');\n                }\n                return null;\n            }\n            let fallbackMode;\n            if (prerenderInfo) {\n                fallbackMode = (0,next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.parseFallbackField)(prerenderInfo.fallback);\n            }\n            // When serving a bot request, we want to serve a blocking render and not\n            // the prerendered page. This ensures that the correct content is served\n            // to the bot in the head.\n            if (fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.PRERENDER && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.isBot)(userAgent)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if ((previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) === -1) {\n                isOnDemandRevalidate = true;\n            }\n            // TODO: adapt for PPR\n            // only allow on-demand revalidate for fallback: true/blocking\n            // or for prerendered fallback: false paths\n            if (isOnDemandRevalidate && (fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND || previousCacheEntry)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if (!minimalMode && fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER && staticPathKey && !didRespond && !isDraftMode && pageIsDynamic && (isProduction || !isPrerendered)) {\n                // if the page has dynamicParams: false and this pathname wasn't\n                // prerendered trigger the no fallback handling\n                if (// In development, fall through to render to handle missing\n                // getStaticPaths.\n                (isProduction || prerenderInfo) && // When fallback isn't present, abort this render so we 404\n                fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND) {\n                    throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError();\n                }\n                let fallbackResponse;\n                if (isRoutePPREnabled && !isRSCRequest) {\n                    // We use the response cache here to handle the revalidation and\n                    // management of the fallback shell.\n                    fallbackResponse = await routeModule.handleResponse({\n                        cacheKey: isProduction ? normalizedSrcPage : null,\n                        req,\n                        nextConfig,\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                        isFallback: true,\n                        prerenderManifest,\n                        isRoutePPREnabled,\n                        responseGenerator: async ()=>doRender({\n                                span,\n                                // We pass `undefined` as rendering a fallback isn't resumed\n                                // here.\n                                postponed: undefined,\n                                fallbackRouteParams: // If we're in production or we're debugging the fallback\n                                // shell then we should postpone when dynamic params are\n                                // accessed.\n                                isProduction || isDebugFallbackShell ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(normalizedSrcPage) : null\n                            }),\n                        waitUntil: ctx.waitUntil\n                    });\n                    // If the fallback response was set to null, then we should return null.\n                    if (fallbackResponse === null) return null;\n                    // Otherwise, if we did get a fallback response, we should return it.\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        return fallbackResponse;\n                    }\n                }\n            }\n            // Only requests that aren't revalidating can be resumed. If we have the\n            // minimal postponed data, then we should resume the render with it.\n            const postponed = !isOnDemandRevalidate && !isRevalidating && minimalPostponed ? minimalPostponed : undefined;\n            // When we're in minimal mode, if we're trying to debug the static shell,\n            // we should just return nothing instead of resuming the dynamic render.\n            if ((isDebugStaticShell || isDebugDynamicAccesses) && typeof postponed !== 'undefined') {\n                return {\n                    cacheControl: {\n                        revalidate: 1,\n                        expire: undefined\n                    },\n                    value: {\n                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.PAGES,\n                        html: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                        pageData: {},\n                        headers: undefined,\n                        status: undefined\n                    }\n                };\n            }\n            // If this is a dynamic route with PPR enabled and the default route\n            // matches were set, then we should pass the fallback route params to\n            // the renderer as this is a fallback revalidation request.\n            const fallbackRouteParams = pageIsDynamic && isRoutePPREnabled && ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'renderFallbackShell') || isDebugFallbackShell) ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(pathname) : null;\n            // Perform the render.\n            return doRender({\n                span,\n                postponed,\n                fallbackRouteParams\n            });\n        };\n        const handleResponse = async (span)=>{\n            var _cacheEntry_value, _cachedData_headers;\n            const cacheEntry = await routeModule.handleResponse({\n                cacheKey: ssgCacheKey,\n                responseGenerator: (c)=>responseGenerator({\n                        span,\n                        ...c\n                    }),\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                isOnDemandRevalidate,\n                isRoutePPREnabled,\n                req,\n                nextConfig,\n                prerenderManifest,\n                waitUntil: ctx.waitUntil\n            });\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            if (!cacheEntry) {\n                if (ssgCacheKey) {\n                    // A cache entry might not be generated if a response is written\n                    // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n                    // have a cache key. If we do have a cache key but we don't end up\n                    // with a cache entry, then either Next.js or the application has a\n                    // bug that needs fixing.\n                    throw Object.defineProperty(new Error('invariant: cache entry required but not generated'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E62\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                return null;\n            }\n            if (((_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant app-page handler received invalid cache entry ${(_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E707\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            const didPostpone = typeof cacheEntry.value.postponed === 'string';\n            if (isSSG && // We don't want to send a cache header for requests that contain dynamic\n            // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n            // request, then we should set the cache header.\n            !isDynamicRSCRequest && (!didPostpone || isPrefetchRSCRequest)) {\n                if (!minimalMode) {\n                    // set x-nextjs-cache header to match the header\n                    // we set for the image-optimizer\n                    res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n                }\n                // Set a header used by the client router to signal the response is static\n                // and should respect the `static` cache staleTime value.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_IS_PRERENDER_HEADER, '1');\n            }\n            const { value: cachedData } = cacheEntry;\n            // Coerce the cache control parameter from the render.\n            let cacheControl;\n            // If this is a resume request in minimal mode it is streamed with dynamic\n            // content and should not be cached.\n            if (minimalPostponed) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (minimalMode && isRSCRequest && !isPrefetchRSCRequest && isRoutePPREnabled) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (!routeModule.isDev) {\n                // If this is a preview mode request, we shouldn't cache it\n                if (isDraftMode) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                } else if (!isSSG) {\n                    if (!res.getHeader('Cache-Control')) {\n                        cacheControl = {\n                            revalidate: 0,\n                            expire: undefined\n                        };\n                    }\n                } else if (cacheEntry.cacheControl) {\n                    // If the cache entry has a cache control with a revalidate value that's\n                    // a number, use it.\n                    if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n                        var _cacheEntry_cacheControl;\n                        if (cacheEntry.cacheControl.revalidate < 1) {\n                            throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                                value: \"E22\",\n                                enumerable: false,\n                                configurable: true\n                            });\n                        }\n                        cacheControl = {\n                            revalidate: cacheEntry.cacheControl.revalidate,\n                            expire: ((_cacheEntry_cacheControl = cacheEntry.cacheControl) == null ? void 0 : _cacheEntry_cacheControl.expire) ?? nextConfig.expireTime\n                        };\n                    } else {\n                        cacheControl = {\n                            revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.CACHE_ONE_YEAR,\n                            expire: undefined\n                        };\n                    }\n                }\n            }\n            cacheEntry.cacheControl = cacheControl;\n            if (typeof segmentPrefetchHeader === 'string' && (cachedData == null ? void 0 : cachedData.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE && cachedData.segmentData) {\n                var _cachedData_headers1;\n                // This is a prefetch request issued by the client Segment Cache. These\n                // should never reach the application layer (lambda). We should either\n                // respond from the cache (HIT) or respond with 204 No Content (MISS).\n                // Set a header to indicate that PPR is enabled for this route. This\n                // lets the client distinguish between a regular cache miss and a cache\n                // miss due to PPR being disabled. In other contexts this header is used\n                // to indicate that the response contains dynamic data, but here we're\n                // only using it to indicate that the feature is enabled — the segment\n                // response itself contains whether the data is dynamic.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '2');\n                // Add the cache tags header to the response if it exists and we're in\n                // minimal mode while rendering a static page.\n                const tags = (_cachedData_headers1 = cachedData.headers) == null ? void 0 : _cachedData_headers1[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                    res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n                }\n                const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader);\n                if (matchedSegment !== undefined) {\n                    // Cache hit\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(matchedSegment),\n                        cacheControl: cacheEntry.cacheControl\n                    });\n                }\n                // Cache miss. Either a cache entry for this route has not been generated\n                // (which technically should not be possible when PPR is enabled, because\n                // at a minimum there should always be a fallback entry) or there's no\n                // match for the requested segment. Respond with a 204 No Content. We\n                // don't bother to respond with 404, because these requests are only\n                // issued as part of a prefetch.\n                res.statusCode = 204;\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If there's a callback for `onCacheEntry`, call it with the cache entry\n            // and the revalidate options.\n            const onCacheEntry = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'onCacheEntry');\n            if (onCacheEntry) {\n                const finished = await onCacheEntry({\n                    ...cacheEntry,\n                    // TODO: remove this when upstream doesn't\n                    // always expect this value to be \"PAGE\"\n                    value: {\n                        ...cacheEntry.value,\n                        kind: 'PAGE'\n                    }\n                }, {\n                    url: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'initURL')\n                });\n                if (finished) {\n                    // TODO: maybe we have to end the request?\n                    return null;\n                }\n            }\n            // If the request has a postponed state and it's a resume request we\n            // should error.\n            if (didPostpone && minimalPostponed) {\n                throw Object.defineProperty(new Error('Invariant: postponed state should not be present on a resume request'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E396\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (cachedData.headers) {\n                const headers = {\n                    ...cachedData.headers\n                };\n                if (!minimalMode || !isSSG) {\n                    delete headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                }\n                for (let [key, value] of Object.entries(headers)){\n                    if (typeof value === 'undefined') continue;\n                    if (Array.isArray(value)) {\n                        for (const v of value){\n                            res.appendHeader(key, v);\n                        }\n                    } else if (typeof value === 'number') {\n                        value = value.toString();\n                        res.appendHeader(key, value);\n                    } else {\n                        res.appendHeader(key, value);\n                    }\n                }\n            }\n            // Add the cache tags header to the response if it exists and we're in\n            // minimal mode while rendering a static page.\n            const tags = (_cachedData_headers = cachedData.headers) == null ? void 0 : _cachedData_headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n            if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n            }\n            // If the request is a data request, then we shouldn't set the status code\n            // from the response because it should always be 200. This should be gated\n            // behind the experimental PPR flag.\n            if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n                res.statusCode = cachedData.status;\n            }\n            // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n            if (!minimalMode && cachedData.status && next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__.RedirectStatusCode[cachedData.status] && isRSCRequest) {\n                res.statusCode = 200;\n            }\n            // Mark that the request did postpone.\n            if (didPostpone) {\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '1');\n            }\n            // we don't go through this block when preview mode is true\n            // as preview mode is a dynamic request (bypasses cache) and doesn't\n            // generate both HTML and payloads in the same request so continue to just\n            // return the generated payload\n            if (isRSCRequest && !isDraftMode) {\n                // If this is a dynamic RSC request, then stream the response.\n                if (typeof cachedData.rscData === 'undefined') {\n                    if (cachedData.postponed) {\n                        throw Object.defineProperty(new Error('Invariant: Expected postponed to be undefined'), \"__NEXT_ERROR_CODE\", {\n                            value: \"E372\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: cachedData.html,\n                        // Dynamic RSC responses cannot be cached, even if they're\n                        // configured with `force-static` because we have no way of\n                        // distinguishing between `force-static` and pages that have no\n                        // postponed state.\n                        // TODO: distinguish `force-static` from pages with no postponed state (static)\n                        cacheControl: isDynamicRSCRequest ? {\n                            revalidate: 0,\n                            expire: undefined\n                        } : cacheEntry.cacheControl\n                    });\n                }\n                // As this isn't a prefetch request, we should serve the static flight\n                // data.\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(cachedData.rscData),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // This is a request for HTML data.\n            let body = cachedData.html;\n            // If there's no postponed state, we should just serve the HTML. This\n            // should also be the case for a resume request because it's completed\n            // as a server render (rather than a static render).\n            if (!didPostpone || minimalMode) {\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If we're debugging the static shell or the dynamic API accesses, we\n            // should just serve the HTML without resuming the render. The returned\n            // HTML will be the static shell so all the Dynamic API's will be used\n            // during static generation.\n            if (isDebugStaticShell || isDebugDynamicAccesses) {\n                // Since we're not resuming the render, we need to at least add the\n                // closing body and html tags to create valid HTML.\n                body.chain(new ReadableStream({\n                    start (controller) {\n                        controller.enqueue(next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__.ENCODED_TAGS.CLOSED.BODY_AND_HTML);\n                        controller.close();\n                    }\n                }));\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: {\n                        revalidate: 0,\n                        expire: undefined\n                    }\n                });\n            }\n            // This request has postponed, so let's create a new transformer that the\n            // dynamic data can pipe to that will attach the dynamic data to the end\n            // of the response.\n            const transformer = new TransformStream();\n            body.chain(transformer.readable);\n            // Perform the render again, but this time, provide the postponed state.\n            // We don't await because we want the result to start streaming now, and\n            // we've already chained the transformer's readable to the render result.\n            doRender({\n                span,\n                postponed: cachedData.postponed,\n                // This is a resume render, not a fallback render, so we don't need to\n                // set this.\n                fallbackRouteParams: null\n            }).then(async (result)=>{\n                var _result_value;\n                if (!result) {\n                    throw Object.defineProperty(new Error('Invariant: expected a result to be returned'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E463\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if (((_result_value = result.value) == null ? void 0 : _result_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                    var _result_value1;\n                    throw Object.defineProperty(new Error(`Invariant: expected a page response, got ${(_result_value1 = result.value) == null ? void 0 : _result_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                        value: \"E305\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                // Pipe the resume result to the transformer.\n                await result.value.html.pipeTo(transformer.writable);\n            }).catch((err)=>{\n                // An error occurred during piping or preparing the render, abort\n                // the transformers writer so we can terminate the stream.\n                transformer.writable.abort(err).catch((e)=>{\n                    console.error(\"couldn't abort transformer\", e);\n                });\n            });\n            return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                req,\n                res,\n                type: 'html',\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                result: body,\n                // We don't want to cache the response if it has postponed data because\n                // the response being sent to the client it's dynamic parts are streamed\n                // to the client on the same request.\n                cacheControl: {\n                    revalidate: 0,\n                    expire: undefined\n                }\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            return await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: srcPage,\n                routeType: 'render',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__.getRevalidateReason)({\n                    isRevalidate: isSSG,\n                    isOnDemandRevalidate\n                })\n            }, routerServerContext);\n        }\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(dashboard)%2Fdashboard%2Fpage&page=%2F(dashboard)%2Fdashboard%2Fpage&appPaths=%2F(dashboard)%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fdashboard%2Fpage.js&appDir=D%3A%5CProjects%5CCpa-dashboard%5Ccpa-dashboard%5Ccpa-dashboard%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5CCpa-dashboard%5Ccpa-dashboard%5Ccpa-dashboard%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(rsc)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(rsc)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(rsc)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cerror-boundary.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoast.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Chooks%5C%5CuseAuth.js%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cerror-boundary.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoast.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Chooks%5C%5CuseAuth.js%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/error-boundary.jsx */ \"(rsc)/./src/components/ui/error-boundary.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/toast.jsx */ \"(rsc)/./src/components/ui/toast.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/hooks/useAuth.js */ \"(rsc)/./src/hooks/useAuth.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cerror-boundary.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoast.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Chooks%5C%5CuseAuth.js%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Cdashboard%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Cdashboard%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(dashboard)/dashboard/page.js */ \"(rsc)/./src/app/(dashboard)/dashboard/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q0NwYS1kYXNoYm9hcmQlNUMlNUNjcGEtZGFzaGJvYXJkJTVDJTVDY3BhLWRhc2hib2FyZCU1QyU1Q2Zyb250ZW5kJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDKGRhc2hib2FyZCklNUMlNUNkYXNoYm9hcmQlNUMlNUNwYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwTEFBbUoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2plY3RzXFxcXENwYS1kYXNoYm9hcmRcXFxcY3BhLWRhc2hib2FyZFxcXFxjcGEtZGFzaGJvYXJkXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcKGRhc2hib2FyZClcXFxcZGFzaGJvYXJkXFxcXHBhZ2UuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Cdashboard%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0c1xcQ3BhLWRhc2hib2FyZFxcY3BhLWRhc2hib2FyZFxcY3BhLWRhc2hib2FyZFxcZnJvbnRlbmRcXHNyY1xcYXBwXFxmYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/(dashboard)/dashboard/page.js":
/*!***********************************************!*\
  !*** ./src/app/(dashboard)/dashboard/page.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\app\\(dashboard)\\dashboard\\page.js",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/(dashboard)/layout.js":
/*!***************************************!*\
  !*** ./src/app/(dashboard)/layout.js ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction DashboardLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\layout.js\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwLyhkYXNoYm9hcmQpL2xheW91dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWUsU0FBU0EsZ0JBQWdCLEVBQUVDLFFBQVEsRUFBRTtJQUNsRCxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTtrQkFDWkY7Ozs7OztBQUdQIiwic291cmNlcyI6WyJEOlxcUHJvamVjdHNcXENwYS1kYXNoYm9hcmRcXGNwYS1kYXNoYm9hcmRcXGNwYS1kYXNoYm9hcmRcXGZyb250ZW5kXFxzcmNcXGFwcFxcKGRhc2hib2FyZClcXGxheW91dC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBEYXNoYm9hcmRMYXlvdXQoeyBjaGlsZHJlbiB9KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTBcIj5cclxuICAgICAge2NoaWxkcmVufVxyXG4gICAgPC9kaXY+XHJcbiAgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJEYXNoYm9hcmRMYXlvdXQiLCJjaGlsZHJlbiIsImRpdiIsImNsYXNzTmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/(dashboard)/layout.js\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"71db2b57e230\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJEOlxcUHJvamVjdHNcXENwYS1kYXNoYm9hcmRcXGNwYS1kYXNoYm9hcmRcXGNwYS1kYXNoYm9hcmRcXGZyb250ZW5kXFxzcmNcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI3MWRiMmI1N2UyMzBcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.js":
/*!***************************!*\
  !*** ./src/app/layout.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-geist-sans\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistSans\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-sans\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistSans\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.js\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.js\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"(rsc)/./src/hooks/useAuth.js\");\n/* harmony import */ var _components_ui_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/toast */ \"(rsc)/./src/components/ui/toast.jsx\");\n/* harmony import */ var _components_ui_error_boundary__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/error-boundary */ \"(rsc)/./src/components/ui/error-boundary.jsx\");\n\n\n\n\n\n\n\nconst metadata = {\n    title: \"Create Next App\",\n    description: \"Generated by create next app\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_js_import_Geist_arguments_variable_font_geist_sans_subsets_latin_variableName_geistSans___WEBPACK_IMPORTED_MODULE_5___default().variable)} ${(next_font_google_target_css_path_src_app_layout_js_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6___default().variable)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_error_boundary__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toast__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\layout.js\",\n                        lineNumber: 30,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\layout.js\",\n                    lineNumber: 29,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\layout.js\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\layout.js\",\n            lineNumber: 25,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\layout.js\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.js\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/error-boundary.jsx":
/*!**********************************************!*\
  !*** ./src/components/ui/error-boundary.jsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\components\\ui\\error-boundary.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/ui/toast.jsx":
/*!*************************************!*\
  !*** ./src/components/ui/toast.jsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__),
/* harmony export */   useToast: () => (/* binding */ useToast)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

const ToastProvider = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ToastProvider() from the server but ToastProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\components\\ui\\toast.jsx",
"ToastProvider",
);const useToast = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useToast() from the server but useToast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\components\\ui\\toast.jsx",
"useToast",
);/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\components\\ui\\toast.jsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/hooks/useAuth.js":
/*!******************************!*\
  !*** ./src/hooks/useAuth.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),
/* harmony export */   useAuth: () => (/* binding */ useAuth)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

const AuthProvider = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\hooks\\useAuth.js",
"AuthProvider",
);const useAuth = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"D:\\Projects\\Cpa-dashboard\\cpa-dashboard\\cpa-dashboard\\frontend\\src\\hooks\\useAuth.js",
"useAuth",
);

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/builtin/global-error.js */ \"(ssr)/./node_modules/next/dist/client/components/builtin/global-error.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(ssr)/./node_modules/next/dist/lib/metadata/generate/icon-mark.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(ssr)/./node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cbuiltin%5C%5Cglobal-error.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Clib%5C%5Cmetadata%5C%5Cgenerate%5C%5Cicon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cnext-devtools%5C%5Cuserspace%5C%5Capp%5C%5Csegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cerror-boundary.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoast.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Chooks%5C%5CuseAuth.js%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cerror-boundary.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoast.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Chooks%5C%5CuseAuth.js%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/error-boundary.jsx */ \"(ssr)/./src/components/ui/error-boundary.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/toast.jsx */ \"(ssr)/./src/components/ui/toast.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/hooks/useAuth.js */ \"(ssr)/./src/hooks/useAuth.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-sans%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistSans%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.js%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Cerror-boundary.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Ctoast.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Chooks%5C%5CuseAuth.js%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Cdashboard%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Cdashboard%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(dashboard)/dashboard/page.js */ \"(ssr)/./src/app/(dashboard)/dashboard/page.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q0NwYS1kYXNoYm9hcmQlNUMlNUNjcGEtZGFzaGJvYXJkJTVDJTVDY3BhLWRhc2hib2FyZCU1QyU1Q2Zyb250ZW5kJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDKGRhc2hib2FyZCklNUMlNUNkYXNoYm9hcmQlNUMlNUNwYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwTEFBbUoiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFByb2plY3RzXFxcXENwYS1kYXNoYm9hcmRcXFxcY3BhLWRhc2hib2FyZFxcXFxjcGEtZGFzaGJvYXJkXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxhcHBcXFxcKGRhc2hib2FyZClcXFxcZGFzaGJvYXJkXFxcXHBhZ2UuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CProjects%5C%5CCpa-dashboard%5C%5Ccpa-dashboard%5C%5Ccpa-dashboard%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5C(dashboard)%5C%5Cdashboard%5C%5Cpage.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/(dashboard)/dashboard/page.js":
/*!***********************************************!*\
  !*** ./src/app/(dashboard)/dashboard/page.js ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_common_Header__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/common/Header */ \"(ssr)/./src/components/common/Header.jsx\");\n/* harmony import */ var _components_dashboard_Dashboard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/Dashboard */ \"(ssr)/./src/components/dashboard/Dashboard.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction DashboardPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_common_Header__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                clientLogo: \"/clientLogo.png\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"p-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_Dashboard__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\dashboard\\\\page.js\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwLyhkYXNoYm9hcmQpL2Rhc2hib2FyZC9wYWdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUVnRDtBQUNTO0FBRTFDLFNBQVNFO0lBQ3RCLHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0osaUVBQU1BO2dCQUFDSyxZQUFXOzs7Ozs7MEJBQ25CLDhEQUFDQztnQkFBS0YsV0FBVTswQkFDZCw0RUFBQ0gsdUVBQVNBOzs7Ozs7Ozs7Ozs7Ozs7O0FBSWxCIiwic291cmNlcyI6WyJEOlxcUHJvamVjdHNcXENwYS1kYXNoYm9hcmRcXGNwYS1kYXNoYm9hcmRcXGNwYS1kYXNoYm9hcmRcXGZyb250ZW5kXFxzcmNcXGFwcFxcKGRhc2hib2FyZClcXGRhc2hib2FyZFxccGFnZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuXHJcbmltcG9ydCBIZWFkZXIgZnJvbSBcIkAvY29tcG9uZW50cy9jb21tb24vSGVhZGVyXCI7XHJcbmltcG9ydCBEYXNoYm9hcmQgZnJvbSBcIkAvY29tcG9uZW50cy9kYXNoYm9hcmQvRGFzaGJvYXJkXCI7XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBEYXNoYm9hcmRQYWdlKCkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1ncmF5LTEwMFwiPlxyXG4gICAgICA8SGVhZGVyIGNsaWVudExvZ289XCIvY2xpZW50TG9nby5wbmdcIiAvPlxyXG4gICAgICA8bWFpbiBjbGFzc05hbWU9XCJwLTNcIj5cclxuICAgICAgICA8RGFzaGJvYXJkIC8+XHJcbiAgICAgIDwvbWFpbj5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbIkhlYWRlciIsIkRhc2hib2FyZCIsIkRhc2hib2FyZFBhZ2UiLCJkaXYiLCJjbGFzc05hbWUiLCJjbGllbnRMb2dvIiwibWFpbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(dashboard)/dashboard/page.js\n");

/***/ }),

/***/ "(ssr)/./src/components/common/Header.jsx":
/*!******************************************!*\
  !*** ./src/components/common/Header.jsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_tokenStorage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/tokenStorage */ \"(ssr)/./src/lib/tokenStorage.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Header({ filters, clientLogo }) {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [userName, setUserName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"John Doe\");\n    const [role, setRole] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"Admin\");\n    const [showProfileDropdown, setShowProfileDropdown] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const profileRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            const user = _lib_tokenStorage__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getUserData();\n            const name = user?.name || \"John Doe\";\n            const userRole = user?.role?.name || \"Admin\";\n            setUserName(name);\n            setRole(userRole);\n        }\n    }[\"Header.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Header.useEffect\": ()=>{\n            function handleClickOutside(event) {\n                if (profileRef.current && !profileRef.current.contains(event.target)) {\n                    setShowProfileDropdown(false);\n                }\n            }\n            document.addEventListener(\"mousedown\", handleClickOutside);\n            return ({\n                \"Header.useEffect\": ()=>{\n                    document.removeEventListener(\"mousedown\", handleClickOutside);\n                }\n            })[\"Header.useEffect\"];\n        }\n    }[\"Header.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"bg-white border-b border-gray-200 px-6 py-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: \"/advisorLogo.png\",\n                            alt: \"BG Advisors CPA Logo\",\n                            className: \"h-12 w-auto mr-3\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\common\\\\Header.jsx\",\n                            lineNumber: 41,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-10 border-l border-gray-300 mx-1\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\common\\\\Header.jsx\",\n                            lineNumber: 47,\n                            columnNumber: 11\n                        }, this),\n                        clientLogo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: clientLogo,\n                            alt: \"Client Logo\",\n                            className: \"h-12 w-auto mr-3\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\common\\\\Header.jsx\",\n                            lineNumber: 49,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-2xl font-bold text-[#1A2341] ml-3\",\n                            children: \"CPA Pro\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\common\\\\Header.jsx\",\n                            lineNumber: 55,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\common\\\\Header.jsx\",\n                    lineNumber: 40,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-6\",\n                    children: filters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mr-4\",\n                        children: filters\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\common\\\\Header.jsx\",\n                        lineNumber: 64,\n                        columnNumber: 23\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\common\\\\Header.jsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative flex items-center\",\n                    role: \"button\",\n                    tabIndex: 0,\n                    onKeyDown: (e)=>{\n                        if (e.key === \"Enter\" || e.key === \" \") {\n                            setShowProfileDropdown((v)=>!v);\n                        }\n                    },\n                    ref: profileRef,\n                    onClick: ()=>setShowProfileDropdown((v)=>!v),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"w-11 h-11 rounded-full bg-gray-200 flex items-center justify-center text-lg font-bold text-[#222] border border-gray-300 shadow-sm\",\n                                    children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.getInitials)(userName)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\common\\\\Header.jsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col cursor-pointer gap-0.5\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold text-[#222] text-base leading-tight\",\n                                            children: userName\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\common\\\\Header.jsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-400 leading-tight\",\n                                            children: role\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\common\\\\Header.jsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\common\\\\Header.jsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 text-gray-400 ml-1\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M19 9l-7 7-7-7\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\common\\\\Header.jsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\common\\\\Header.jsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\common\\\\Header.jsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        showProfileDropdown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute right-0 top-full mt-2 w-40 bg-white border border-gray-200 rounded-lg shadow-md z-10\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"block w-full px-4 py-2 text-left hover:bg-gray-100\",\n                                    onClick: ()=>{\n                                        /* handle profile */ setShowProfileDropdown(false);\n                                    },\n                                    children: \"Profile\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\common\\\\Header.jsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"block w-full px-4 py-2 text-left hover:bg-gray-100\",\n                                    onClick: ()=>{\n                                        setShowProfileDropdown(false);\n                                        router.push(\"/login\");\n                                    },\n                                    children: \"Logout\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\common\\\\Header.jsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\common\\\\Header.jsx\",\n                            lineNumber: 107,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\common\\\\Header.jsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\common\\\\Header.jsx\",\n            lineNumber: 38,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\common\\\\Header.jsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/common/Header.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/Dashboard.jsx":
/*!************************************************!*\
  !*** ./src/components/dashboard/Dashboard.jsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../ui/button */ \"(ssr)/./src/components/ui/button.jsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_dashboard_PDFViewer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/dashboard/PDFViewer */ \"(ssr)/./src/components/dashboard/PDFViewer.jsx\");\n/* harmony import */ var _lib_tokenStorage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/tokenStorage */ \"(ssr)/./src/lib/tokenStorage.js\");\n/* harmony import */ var _data_dashboard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/data/dashboard */ \"(ssr)/./src/data/dashboard.js\");\n/* harmony import */ var _ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../ui/dropdown-menu */ \"(ssr)/./src/components/ui/dropdown-menu.jsx\");\n\n\n\n\n\n\n\n\n\nfunction Dashboard() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [months, setMonths] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"July\");\n    const [isUserAdmin, setIsUserAdmin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            const userData = _lib_tokenStorage__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getUserData();\n            setIsUserAdmin(userData?.role?.name === \"admin\");\n        }\n    }[\"Dashboard.useEffect\"], []);\n    const handleBack = ()=>{\n        router.push(\"/listing\");\n    };\n    const pdfPath = _data_dashboard__WEBPACK_IMPORTED_MODULE_6__.monthToFile[months] || \"\";\n    if (!pdfPath) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64 text-gray-500\",\n            children: \"No PDF file provided.\"\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.jsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center mb-4 gap-4\",\n                children: [\n                    isUserAdmin ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"outline\",\n                        leftIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.jsx\",\n                            lineNumber: 40,\n                            columnNumber: 23\n                        }, void 0),\n                        onClick: ()=>handleBack(),\n                        children: \"Back to Listing\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.jsx\",\n                        lineNumber: 38,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {}, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.jsx\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenu, {\n                                selected: months,\n                                onSelect: setMonths,\n                                options: [\n                                    \"May\",\n                                    \"June\",\n                                    \"July\"\n                                ],\n                                customHeight: \"40px\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.jsx\",\n                                lineNumber: 49,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"default\",\n                                children: \"Download Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.jsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.jsx\",\n                        lineNumber: 48,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.jsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_PDFViewer__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                url: pdfPath\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.jsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\dashboard\\\\Dashboard.jsx\",\n        lineNumber: 35,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/Dashboard.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/dashboard/PDFViewer.jsx":
/*!************************************************!*\
  !*** ./src/components/dashboard/PDFViewer.jsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var pdfjs_dist__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! pdfjs-dist */ \"(ssr)/./node_modules/pdfjs-dist/build/pdf.js\");\n/* harmony import */ var pdfjs_dist__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(pdfjs_dist__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var pdfjs_dist_build_pdf_worker_min_js_url__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! pdfjs-dist/build/pdf.worker.min.js?url */ \"(ssr)/./node_modules/pdfjs-dist/build/pdf.worker.min.js?url\");\n/* harmony import */ var pdfjs_dist_build_pdf_worker_min_js_url__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(pdfjs_dist_build_pdf_worker_min_js_url__WEBPACK_IMPORTED_MODULE_3__);\n// components/PdfCanvasViewer.js\n\n\n\n\npdfjs_dist__WEBPACK_IMPORTED_MODULE_2__.GlobalWorkerOptions.workerSrc = (pdfjs_dist_build_pdf_worker_min_js_url__WEBPACK_IMPORTED_MODULE_3___default());\nconst PdfCanvasViewer = ({ url })=>{\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const renderTaskRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PdfCanvasViewer.useEffect\": ()=>{\n            const renderPdf = {\n                \"PdfCanvasViewer.useEffect.renderPdf\": async ()=>{\n                    try {\n                        const loadingTask = pdfjs_dist__WEBPACK_IMPORTED_MODULE_2__.getDocument(url);\n                        const pdf = await loadingTask.promise;\n                        const page = await pdf.getPage(1);\n                        const desiredWidth = window.innerWidth * 0.98;\n                        const viewport = page.getViewport({\n                            scale: 1\n                        });\n                        const scale = desiredWidth / viewport.width;\n                        const scaledViewport = page.getViewport({\n                            scale\n                        });\n                        const canvas = canvasRef.current;\n                        const context = canvas.getContext(\"2d\");\n                        // High-DPI screens for sharp rendering\n                        const dpr = window.devicePixelRatio || 1;\n                        canvas.width = scaledViewport.width * dpr;\n                        canvas.height = scaledViewport.height * dpr;\n                        canvas.style.width = `${scaledViewport.width}px`;\n                        canvas.style.height = `${scaledViewport.height}px`;\n                        context.setTransform(dpr, 0, 0, dpr, 0, 0);\n                        if (renderTaskRef.current) {\n                            renderTaskRef.current.cancel();\n                        }\n                        renderTaskRef.current = page.render({\n                            canvasContext: context,\n                            viewport: scaledViewport\n                        });\n                        await renderTaskRef.current.promise;\n                    } catch (err) {\n                        if (err?.name !== \"RenderingCancelledException\") {\n                            console.error(\"PDF render error:\", err);\n                        }\n                    }\n                }\n            }[\"PdfCanvasViewer.useEffect.renderPdf\"];\n            renderPdf();\n            return ({\n                \"PdfCanvasViewer.useEffect\": ()=>{\n                    if (renderTaskRef.current) {\n                        renderTaskRef.current.cancel();\n                    }\n                }\n            })[\"PdfCanvasViewer.useEffect\"];\n        }\n    }[\"PdfCanvasViewer.useEffect\"], [\n        url\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            display: \"flex\",\n            justifyContent: \"center\",\n            width: \"100%\"\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n            ref: canvasRef,\n            style: {\n                display: \"block\",\n                width: \"98vw\",\n                height: \"auto\",\n                margin: 0,\n                padding: 0,\n                background: \"none\",\n                boxShadow: \"none\",\n                border: \"none\"\n            }\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\dashboard\\\\PDFViewer.jsx\",\n            lineNumber: 64,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\dashboard\\\\PDFViewer.jsx\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PdfCanvasViewer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9kYXNoYm9hcmQvUERGVmlld2VyLmpzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBQSxnQ0FBZ0M7O0FBQ1U7QUFDSDtBQUMwQjtBQUVqRUUsMkRBQTRCLENBQUNHLFNBQVMsR0FBR0YsK0VBQVdBO0FBRXBELE1BQU1HLGtCQUFrQixDQUFDLEVBQUVDLEdBQUcsRUFBRTtJQUM5QixNQUFNQyxZQUFZUCw2Q0FBTUEsQ0FBQztJQUN6QixNQUFNUSxnQkFBZ0JSLDZDQUFNQSxDQUFDO0lBRTdCRCxnREFBU0E7cUNBQUM7WUFDUixNQUFNVTt1REFBWTtvQkFDaEIsSUFBSTt3QkFDRixNQUFNQyxjQUFjVCxtREFBb0IsQ0FBQ0s7d0JBQ3pDLE1BQU1NLE1BQU0sTUFBTUYsWUFBWUcsT0FBTzt3QkFFckMsTUFBTUMsT0FBTyxNQUFNRixJQUFJRyxPQUFPLENBQUM7d0JBQy9CLE1BQU1DLGVBQWVDLE9BQU9DLFVBQVUsR0FBRzt3QkFFekMsTUFBTUMsV0FBV0wsS0FBS00sV0FBVyxDQUFDOzRCQUFFQyxPQUFPO3dCQUFFO3dCQUM3QyxNQUFNQSxRQUFRTCxlQUFlRyxTQUFTRyxLQUFLO3dCQUMzQyxNQUFNQyxpQkFBaUJULEtBQUtNLFdBQVcsQ0FBQzs0QkFBRUM7d0JBQU07d0JBRWhELE1BQU1HLFNBQVNqQixVQUFVa0IsT0FBTzt3QkFDaEMsTUFBTUMsVUFBVUYsT0FBT0csVUFBVSxDQUFDO3dCQUVsQyx1Q0FBdUM7d0JBQ3ZDLE1BQU1DLE1BQU1YLE9BQU9ZLGdCQUFnQixJQUFJO3dCQUN2Q0wsT0FBT0YsS0FBSyxHQUFHQyxlQUFlRCxLQUFLLEdBQUdNO3dCQUN0Q0osT0FBT00sTUFBTSxHQUFHUCxlQUFlTyxNQUFNLEdBQUdGO3dCQUN4Q0osT0FBT08sS0FBSyxDQUFDVCxLQUFLLEdBQUcsR0FBR0MsZUFBZUQsS0FBSyxDQUFDLEVBQUUsQ0FBQzt3QkFDaERFLE9BQU9PLEtBQUssQ0FBQ0QsTUFBTSxHQUFHLEdBQUdQLGVBQWVPLE1BQU0sQ0FBQyxFQUFFLENBQUM7d0JBQ2xESixRQUFRTSxZQUFZLENBQUNKLEtBQUssR0FBRyxHQUFHQSxLQUFLLEdBQUc7d0JBRXhDLElBQUlwQixjQUFjaUIsT0FBTyxFQUFFOzRCQUN6QmpCLGNBQWNpQixPQUFPLENBQUNRLE1BQU07d0JBQzlCO3dCQUVBekIsY0FBY2lCLE9BQU8sR0FBR1gsS0FBS29CLE1BQU0sQ0FBQzs0QkFDbENDLGVBQWVUOzRCQUNmUCxVQUFVSTt3QkFDWjt3QkFFQSxNQUFNZixjQUFjaUIsT0FBTyxDQUFDWixPQUFPO29CQUNyQyxFQUFFLE9BQU91QixLQUFLO3dCQUNaLElBQUlBLEtBQUtDLFNBQVMsK0JBQStCOzRCQUMvQ0MsUUFBUUMsS0FBSyxDQUFDLHFCQUFxQkg7d0JBQ3JDO29CQUNGO2dCQUNGOztZQUVBM0I7WUFFQTs2Q0FBTztvQkFDTCxJQUFJRCxjQUFjaUIsT0FBTyxFQUFFO3dCQUN6QmpCLGNBQWNpQixPQUFPLENBQUNRLE1BQU07b0JBQzlCO2dCQUNGOztRQUNGO29DQUFHO1FBQUMzQjtLQUFJO0lBRVIscUJBQ0UsOERBQUNrQztRQUFJVCxPQUFPO1lBQUVVLFNBQVM7WUFBUUMsZ0JBQWdCO1lBQVVwQixPQUFPO1FBQU87a0JBQ3JFLDRFQUFDRTtZQUNDbUIsS0FBS3BDO1lBQ0x3QixPQUFPO2dCQUNMVSxTQUFTO2dCQUNUbkIsT0FBTztnQkFDUFEsUUFBUTtnQkFDUmMsUUFBUTtnQkFDUkMsU0FBUztnQkFDVEMsWUFBWTtnQkFDWkMsV0FBVztnQkFDWEMsUUFBUTtZQUNWOzs7Ozs7Ozs7OztBQUlSO0FBRUEsaUVBQWUzQyxlQUFlQSxFQUFDIiwic291cmNlcyI6WyJEOlxcUHJvamVjdHNcXENwYS1kYXNoYm9hcmRcXGNwYS1kYXNoYm9hcmRcXGNwYS1kYXNoYm9hcmRcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXGRhc2hib2FyZFxcUERGVmlld2VyLmpzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBjb21wb25lbnRzL1BkZkNhbnZhc1ZpZXdlci5qc1xyXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVJlZiB9IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgKiBhcyBwZGZqc0xpYiBmcm9tIFwicGRmanMtZGlzdFwiO1xyXG5pbXBvcnQgcGRmanNXb3JrZXIgZnJvbSBcInBkZmpzLWRpc3QvYnVpbGQvcGRmLndvcmtlci5taW4uanM/dXJsXCI7XHJcblxyXG5wZGZqc0xpYi5HbG9iYWxXb3JrZXJPcHRpb25zLndvcmtlclNyYyA9IHBkZmpzV29ya2VyO1xyXG5cclxuY29uc3QgUGRmQ2FudmFzVmlld2VyID0gKHsgdXJsIH0pID0+IHtcclxuICBjb25zdCBjYW52YXNSZWYgPSB1c2VSZWYobnVsbCk7XHJcbiAgY29uc3QgcmVuZGVyVGFza1JlZiA9IHVzZVJlZihudWxsKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IHJlbmRlclBkZiA9IGFzeW5jICgpID0+IHtcclxuICAgICAgdHJ5IHtcclxuICAgICAgICBjb25zdCBsb2FkaW5nVGFzayA9IHBkZmpzTGliLmdldERvY3VtZW50KHVybCk7XHJcbiAgICAgICAgY29uc3QgcGRmID0gYXdhaXQgbG9hZGluZ1Rhc2sucHJvbWlzZTtcclxuXHJcbiAgICAgICAgY29uc3QgcGFnZSA9IGF3YWl0IHBkZi5nZXRQYWdlKDEpO1xyXG4gICAgICAgIGNvbnN0IGRlc2lyZWRXaWR0aCA9IHdpbmRvdy5pbm5lcldpZHRoICogMC45ODtcclxuXHJcbiAgICAgICAgY29uc3Qgdmlld3BvcnQgPSBwYWdlLmdldFZpZXdwb3J0KHsgc2NhbGU6IDEgfSk7XHJcbiAgICAgICAgY29uc3Qgc2NhbGUgPSBkZXNpcmVkV2lkdGggLyB2aWV3cG9ydC53aWR0aDtcclxuICAgICAgICBjb25zdCBzY2FsZWRWaWV3cG9ydCA9IHBhZ2UuZ2V0Vmlld3BvcnQoeyBzY2FsZSB9KTtcclxuXHJcbiAgICAgICAgY29uc3QgY2FudmFzID0gY2FudmFzUmVmLmN1cnJlbnQ7XHJcbiAgICAgICAgY29uc3QgY29udGV4dCA9IGNhbnZhcy5nZXRDb250ZXh0KFwiMmRcIik7XHJcblxyXG4gICAgICAgIC8vIEhpZ2gtRFBJIHNjcmVlbnMgZm9yIHNoYXJwIHJlbmRlcmluZ1xyXG4gICAgICAgIGNvbnN0IGRwciA9IHdpbmRvdy5kZXZpY2VQaXhlbFJhdGlvIHx8IDE7XHJcbiAgICAgICAgY2FudmFzLndpZHRoID0gc2NhbGVkVmlld3BvcnQud2lkdGggKiBkcHI7XHJcbiAgICAgICAgY2FudmFzLmhlaWdodCA9IHNjYWxlZFZpZXdwb3J0LmhlaWdodCAqIGRwcjtcclxuICAgICAgICBjYW52YXMuc3R5bGUud2lkdGggPSBgJHtzY2FsZWRWaWV3cG9ydC53aWR0aH1weGA7XHJcbiAgICAgICAgY2FudmFzLnN0eWxlLmhlaWdodCA9IGAke3NjYWxlZFZpZXdwb3J0LmhlaWdodH1weGA7XHJcbiAgICAgICAgY29udGV4dC5zZXRUcmFuc2Zvcm0oZHByLCAwLCAwLCBkcHIsIDAsIDApO1xyXG5cclxuICAgICAgICBpZiAocmVuZGVyVGFza1JlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgICByZW5kZXJUYXNrUmVmLmN1cnJlbnQuY2FuY2VsKCk7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICByZW5kZXJUYXNrUmVmLmN1cnJlbnQgPSBwYWdlLnJlbmRlcih7XHJcbiAgICAgICAgICBjYW52YXNDb250ZXh0OiBjb250ZXh0LFxyXG4gICAgICAgICAgdmlld3BvcnQ6IHNjYWxlZFZpZXdwb3J0LFxyXG4gICAgICAgIH0pO1xyXG5cclxuICAgICAgICBhd2FpdCByZW5kZXJUYXNrUmVmLmN1cnJlbnQucHJvbWlzZTtcclxuICAgICAgfSBjYXRjaCAoZXJyKSB7XHJcbiAgICAgICAgaWYgKGVycj8ubmFtZSAhPT0gXCJSZW5kZXJpbmdDYW5jZWxsZWRFeGNlcHRpb25cIikge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcihcIlBERiByZW5kZXIgZXJyb3I6XCIsIGVycik7XHJcbiAgICAgICAgfVxyXG4gICAgICB9XHJcbiAgICB9O1xyXG5cclxuICAgIHJlbmRlclBkZigpO1xyXG5cclxuICAgIHJldHVybiAoKSA9PiB7XHJcbiAgICAgIGlmIChyZW5kZXJUYXNrUmVmLmN1cnJlbnQpIHtcclxuICAgICAgICByZW5kZXJUYXNrUmVmLmN1cnJlbnQuY2FuY2VsKCk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcbiAgfSwgW3VybF0pO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBzdHlsZT17eyBkaXNwbGF5OiBcImZsZXhcIiwganVzdGlmeUNvbnRlbnQ6IFwiY2VudGVyXCIsIHdpZHRoOiBcIjEwMCVcIiB9fT5cclxuICAgICAgPGNhbnZhc1xyXG4gICAgICAgIHJlZj17Y2FudmFzUmVmfVxyXG4gICAgICAgIHN0eWxlPXt7XHJcbiAgICAgICAgICBkaXNwbGF5OiBcImJsb2NrXCIsXHJcbiAgICAgICAgICB3aWR0aDogXCI5OHZ3XCIsXHJcbiAgICAgICAgICBoZWlnaHQ6IFwiYXV0b1wiLFxyXG4gICAgICAgICAgbWFyZ2luOiAwLFxyXG4gICAgICAgICAgcGFkZGluZzogMCxcclxuICAgICAgICAgIGJhY2tncm91bmQ6IFwibm9uZVwiLFxyXG4gICAgICAgICAgYm94U2hhZG93OiBcIm5vbmVcIixcclxuICAgICAgICAgIGJvcmRlcjogXCJub25lXCIsXHJcbiAgICAgICAgfX1cclxuICAgICAgLz5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBQZGZDYW52YXNWaWV3ZXI7XHJcbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VSZWYiLCJwZGZqc0xpYiIsInBkZmpzV29ya2VyIiwiR2xvYmFsV29ya2VyT3B0aW9ucyIsIndvcmtlclNyYyIsIlBkZkNhbnZhc1ZpZXdlciIsInVybCIsImNhbnZhc1JlZiIsInJlbmRlclRhc2tSZWYiLCJyZW5kZXJQZGYiLCJsb2FkaW5nVGFzayIsImdldERvY3VtZW50IiwicGRmIiwicHJvbWlzZSIsInBhZ2UiLCJnZXRQYWdlIiwiZGVzaXJlZFdpZHRoIiwid2luZG93IiwiaW5uZXJXaWR0aCIsInZpZXdwb3J0IiwiZ2V0Vmlld3BvcnQiLCJzY2FsZSIsIndpZHRoIiwic2NhbGVkVmlld3BvcnQiLCJjYW52YXMiLCJjdXJyZW50IiwiY29udGV4dCIsImdldENvbnRleHQiLCJkcHIiLCJkZXZpY2VQaXhlbFJhdGlvIiwiaGVpZ2h0Iiwic3R5bGUiLCJzZXRUcmFuc2Zvcm0iLCJjYW5jZWwiLCJyZW5kZXIiLCJjYW52YXNDb250ZXh0IiwiZXJyIiwibmFtZSIsImNvbnNvbGUiLCJlcnJvciIsImRpdiIsImRpc3BsYXkiLCJqdXN0aWZ5Q29udGVudCIsInJlZiIsIm1hcmdpbiIsInBhZGRpbmciLCJiYWNrZ3JvdW5kIiwiYm94U2hhZG93IiwiYm9yZGVyIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/dashboard/PDFViewer.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.jsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.jsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.js\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\", {\n    variants: {\n        variant: {\n            default: \"bg-[#6366F1] text-white shadow-md hover:bg-[#5053C8]\",\n            outline: \"border border-[#6366F1] text-[#6366F1] bg-white shadow-md hover:bg-[#F0F0F0] dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n            destructive: \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n            sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n            lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n            icon: \"size-9\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction Button({ className, variant, size, asChild = false, leftIcon, rightIcon, ...props }) {\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"button\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ...props,\n        children: [\n            leftIcon,\n            props.children,\n            rightIcon\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\button.jsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9idXR0b24uanN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFDYTtBQUNJO0FBRWY7QUFFaEMsTUFBTUksaUJBQWlCRiw2REFBR0EsQ0FDeEIsNGNBQ0E7SUFDRUcsVUFBVTtRQUNSQyxTQUFTO1lBQ1BDLFNBQ0U7WUFDRkMsU0FDRTtZQUNGQyxhQUNFO1lBRUZDLFdBQ0U7WUFDRkMsT0FDRTtZQUNGQyxNQUFNO1FBQ1I7UUFDQUMsTUFBTTtZQUNKTixTQUFTO1lBQ1RPLElBQUk7WUFDSkMsSUFBSTtZQUNKQyxNQUFNO1FBQ1I7SUFDRjtJQUNBQyxpQkFBaUI7UUFDZlgsU0FBUztRQUNUTyxNQUFNO0lBQ1I7QUFDRjtBQUdGLFNBQVNLLE9BQU8sRUFDZEMsU0FBUyxFQUNUYixPQUFPLEVBQ1BPLElBQUksRUFDSk8sVUFBVSxLQUFLLEVBQ2ZDLFFBQVEsRUFDUkMsU0FBUyxFQUNULEdBQUdDLE9BQ0o7SUFDQyxNQUFNQyxPQUFPSixVQUFVbkIsc0RBQUlBLEdBQUc7SUFFOUIscUJBQ0UsOERBQUN1QjtRQUNDQyxhQUFVO1FBQ1ZOLFdBQVdoQiw4Q0FBRUEsQ0FBQ0MsZUFBZTtZQUFFRTtZQUFTTztZQUFNTTtRQUFVO1FBQ3ZELEdBQUdJLEtBQUs7O1lBQ1JGO1lBQ0FFLE1BQU1HLFFBQVE7WUFDZEo7Ozs7Ozs7QUFHUDtBQUVpQyIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RzXFxDcGEtZGFzaGJvYXJkXFxjcGEtZGFzaGJvYXJkXFxjcGEtZGFzaGJvYXJkXFxmcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFx1aVxcYnV0dG9uLmpzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxyXG5pbXBvcnQgeyBTbG90IH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC1zbG90XCJcclxuaW1wb3J0IHsgY3ZhIH0gZnJvbSBcImNsYXNzLXZhcmlhbmNlLWF1dGhvcml0eVwiO1xyXG5cclxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxyXG5cclxuY29uc3QgYnV0dG9uVmFyaWFudHMgPSBjdmEoXHJcbiAgXCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgZ2FwLTIgd2hpdGVzcGFjZS1ub3dyYXAgcm91bmRlZC14bCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTIwMCBkaXNhYmxlZDpwb2ludGVyLWV2ZW50cy1ub25lIGRpc2FibGVkOm9wYWNpdHktNTAgWyZfc3ZnXTpwb2ludGVyLWV2ZW50cy1ub25lIFsmX3N2Zzpub3QoW2NsYXNzKj0nc2l6ZS0nXSldOnNpemUtNCBzaHJpbmstMCBbJl9zdmddOnNocmluay0wIG91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOmJvcmRlci1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1yaW5nLzUwIGZvY3VzLXZpc2libGU6cmluZy1bM3B4XSBhcmlhLWludmFsaWQ6cmluZy1kZXN0cnVjdGl2ZS8yMCBkYXJrOmFyaWEtaW52YWxpZDpyaW5nLWRlc3RydWN0aXZlLzQwIGFyaWEtaW52YWxpZDpib3JkZXItZGVzdHJ1Y3RpdmVcIixcclxuICB7XHJcbiAgICB2YXJpYW50czoge1xyXG4gICAgICB2YXJpYW50OiB7XHJcbiAgICAgICAgZGVmYXVsdDpcclxuICAgICAgICAgIFwiYmctWyM2MzY2RjFdIHRleHQtd2hpdGUgc2hhZG93LW1kIGhvdmVyOmJnLVsjNTA1M0M4XVwiLFxyXG4gICAgICAgIG91dGxpbmU6XHJcbiAgICAgICAgICBcImJvcmRlciBib3JkZXItWyM2MzY2RjFdIHRleHQtWyM2MzY2RjFdIGJnLXdoaXRlIHNoYWRvdy1tZCBob3ZlcjpiZy1bI0YwRjBGMF0gZGFyazpiZy1pbnB1dC8zMCBkYXJrOmJvcmRlci1pbnB1dCBkYXJrOmhvdmVyOmJnLWlucHV0LzUwXCIsXHJcbiAgICAgICAgZGVzdHJ1Y3RpdmU6XHJcbiAgICAgICAgICBcImJnLWRlc3RydWN0aXZlIHRleHQtd2hpdGUgc2hhZG93LXhzIGhvdmVyOmJnLWRlc3RydWN0aXZlLzkwIGZvY3VzLXZpc2libGU6cmluZy1kZXN0cnVjdGl2ZS8yMCBkYXJrOmZvY3VzLXZpc2libGU6cmluZy1kZXN0cnVjdGl2ZS80MCBkYXJrOmJnLWRlc3RydWN0aXZlLzYwXCIsXHJcblxyXG4gICAgICAgIHNlY29uZGFyeTpcclxuICAgICAgICAgIFwiYmctc2Vjb25kYXJ5IHRleHQtc2Vjb25kYXJ5LWZvcmVncm91bmQgc2hhZG93LXhzIGhvdmVyOmJnLXNlY29uZGFyeS84MFwiLFxyXG4gICAgICAgIGdob3N0OlxyXG4gICAgICAgICAgXCJob3ZlcjpiZy1hY2NlbnQgaG92ZXI6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZCBkYXJrOmhvdmVyOmJnLWFjY2VudC81MFwiLFxyXG4gICAgICAgIGxpbms6IFwidGV4dC1wcmltYXJ5IHVuZGVybGluZS1vZmZzZXQtNCBob3Zlcjp1bmRlcmxpbmVcIixcclxuICAgICAgfSxcclxuICAgICAgc2l6ZToge1xyXG4gICAgICAgIGRlZmF1bHQ6IFwiaC05IHB4LTQgcHktMiBoYXMtWz5zdmddOnB4LTNcIixcclxuICAgICAgICBzbTogXCJoLTggcm91bmRlZC1tZCBnYXAtMS41IHB4LTMgaGFzLVs+c3ZnXTpweC0yLjVcIixcclxuICAgICAgICBsZzogXCJoLTEwIHJvdW5kZWQtbWQgcHgtNiBoYXMtWz5zdmddOnB4LTRcIixcclxuICAgICAgICBpY29uOiBcInNpemUtOVwiLFxyXG4gICAgICB9LFxyXG4gICAgfSxcclxuICAgIGRlZmF1bHRWYXJpYW50czoge1xyXG4gICAgICB2YXJpYW50OiBcImRlZmF1bHRcIixcclxuICAgICAgc2l6ZTogXCJkZWZhdWx0XCIsXHJcbiAgICB9LFxyXG4gIH1cclxuKVxyXG5cclxuZnVuY3Rpb24gQnV0dG9uKHtcclxuICBjbGFzc05hbWUsXHJcbiAgdmFyaWFudCxcclxuICBzaXplLFxyXG4gIGFzQ2hpbGQgPSBmYWxzZSxcclxuICBsZWZ0SWNvbixcclxuICByaWdodEljb24sXHJcbiAgLi4ucHJvcHNcclxufSkge1xyXG4gIGNvbnN0IENvbXAgPSBhc0NoaWxkID8gU2xvdCA6IFwiYnV0dG9uXCJcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxDb21wXHJcbiAgICAgIGRhdGEtc2xvdD1cImJ1dHRvblwiXHJcbiAgICAgIGNsYXNzTmFtZT17Y24oYnV0dG9uVmFyaWFudHMoeyB2YXJpYW50LCBzaXplLCBjbGFzc05hbWUgfSkpfVxyXG4gICAgICB7Li4ucHJvcHN9PlxyXG4gICAgICB7bGVmdEljb259XHJcbiAgICAgIHtwcm9wcy5jaGlsZHJlbn1cclxuICAgICAge3JpZ2h0SWNvbn1cclxuICAgIDwvQ29tcD5cclxuICApO1xyXG59XHJcblxyXG5leHBvcnQgeyBCdXR0b24sIGJ1dHRvblZhcmlhbnRzIH1cclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiU2xvdCIsImN2YSIsImNuIiwiYnV0dG9uVmFyaWFudHMiLCJ2YXJpYW50cyIsInZhcmlhbnQiLCJkZWZhdWx0Iiwib3V0bGluZSIsImRlc3RydWN0aXZlIiwic2Vjb25kYXJ5IiwiZ2hvc3QiLCJsaW5rIiwic2l6ZSIsInNtIiwibGciLCJpY29uIiwiZGVmYXVsdFZhcmlhbnRzIiwiQnV0dG9uIiwiY2xhc3NOYW1lIiwiYXNDaGlsZCIsImxlZnRJY29uIiwicmlnaHRJY29uIiwicHJvcHMiLCJDb21wIiwiZGF0YS1zbG90IiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/dropdown-menu.jsx":
/*!*********************************************!*\
  !*** ./src/components/ui/dropdown-menu.jsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DropdownMenu: () => (/* binding */ DropdownMenu),\n/* harmony export */   DropdownMenuCheckboxItem: () => (/* binding */ DropdownMenuCheckboxItem),\n/* harmony export */   DropdownMenuContent: () => (/* binding */ DropdownMenuContent),\n/* harmony export */   DropdownMenuGroup: () => (/* binding */ DropdownMenuGroup),\n/* harmony export */   DropdownMenuItem: () => (/* binding */ DropdownMenuItem),\n/* harmony export */   DropdownMenuLabel: () => (/* binding */ DropdownMenuLabel),\n/* harmony export */   DropdownMenuPortal: () => (/* binding */ DropdownMenuPortal),\n/* harmony export */   DropdownMenuRadioGroup: () => (/* binding */ DropdownMenuRadioGroup),\n/* harmony export */   DropdownMenuRadioItem: () => (/* binding */ DropdownMenuRadioItem),\n/* harmony export */   DropdownMenuSeparator: () => (/* binding */ DropdownMenuSeparator),\n/* harmony export */   DropdownMenuShortcut: () => (/* binding */ DropdownMenuShortcut),\n/* harmony export */   DropdownMenuSub: () => (/* binding */ DropdownMenuSub),\n/* harmony export */   DropdownMenuSubContent: () => (/* binding */ DropdownMenuSubContent),\n/* harmony export */   DropdownMenuSubTrigger: () => (/* binding */ DropdownMenuSubTrigger),\n/* harmony export */   DropdownMenuTrigger: () => (/* binding */ DropdownMenuTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dropdown-menu */ \"(ssr)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_ChevronDownIcon_ChevronRightIcon_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ChevronDownIcon,ChevronRightIcon,CircleIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_ChevronDownIcon_ChevronRightIcon_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ChevronDownIcon,ChevronRightIcon,CircleIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_ChevronDownIcon_ChevronRightIcon_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ChevronDownIcon,ChevronRightIcon,CircleIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_ChevronDownIcon_ChevronRightIcon_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ChevronDownIcon,ChevronRightIcon,CircleIcon!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.js\");\n/* __next_internal_client_entry_do_not_use__ DropdownMenu,DropdownMenuPortal,DropdownMenuTrigger,DropdownMenuContent,DropdownMenuGroup,DropdownMenuLabel,DropdownMenuItem,DropdownMenuCheckboxItem,DropdownMenuRadioGroup,DropdownMenuRadioItem,DropdownMenuSeparator,DropdownMenuShortcut,DropdownMenuSub,DropdownMenuSubTrigger,DropdownMenuSubContent auto */ \n\n\n\n\nfunction DropdownMenu({ label, options = [], selected, onSelect, buttonClassName = \"px-4 py-3 rounded-xl border-none shadow-md bg-white text-[#7C7C9A] focus:outline-none focus:ring-2 focus:ring-[#6C63FF] flex items-center justify-between w-40 transition-all duration-200 hover:shadow-lg\", menuClassName = \"w-40\", renderOption, customHeight, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        \"data-slot\": \"dropdown-menu\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Trigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    className: buttonClassName + \" text-gray-700 border-2 border-blue-500\" + (customHeight ? ` h-[${customHeight}]` : \"\"),\n                    type: \"button\",\n                    style: customHeight ? {\n                        height: customHeight\n                    } : undefined,\n                    children: [\n                        label || selected,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ChevronDownIcon_ChevronRightIcon_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"w-4 h-4 ml-2 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n                    lineNumber: 28,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content, {\n                    className: menuClassName + \" mt-2 rounded-lg shadow-lg min-w-[var(--radix-popper-anchor-width)] max-h-50 overflow-y-auto\",\n                    style: {\n                        background: \"#fff\"\n                    },\n                    children: options.map((option, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item, {\n                            onSelect: ()=>onSelect(option.value || option),\n                            className: \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none w-full\",\n                            style: {\n                                minWidth: \"100%\"\n                            },\n                            children: renderOption ? renderOption(option) : option.label || option\n                        }, option.value || option, false, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n                            lineNumber: 50,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\nfunction DropdownMenuPortal({ ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        \"data-slot\": \"dropdown-menu-portal\",\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\nfunction DropdownMenuTrigger({ ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Trigger, {\n        \"data-slot\": \"dropdown-menu-trigger\",\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\nfunction DropdownMenuContent({ className, sideOffset = 4, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            \"data-slot\": \"dropdown-menu-content\",\n            sideOffset: sideOffset,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-white text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md p-1 shadow-md\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n            lineNumber: 83,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n        lineNumber: 82,\n        columnNumber: 5\n    }, this);\n}\nfunction DropdownMenuGroup({ ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Group, {\n        \"data-slot\": \"dropdown-menu-group\",\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, this);\n}\nfunction DropdownMenuItem({ className, inset, variant = \"default\", ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        \"data-slot\": \"dropdown-menu-item\",\n        \"data-inset\": inset,\n        \"data-variant\": variant,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, this);\n}\nfunction DropdownMenuCheckboxItem({ className, children, checked, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem, {\n        \"data-slot\": \"dropdown-menu-checkbox-item\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\", className),\n        checked: checked,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ChevronDownIcon_ChevronRightIcon_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"size-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n                        lineNumber: 130,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n                lineNumber: 128,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\nfunction DropdownMenuRadioGroup({ ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioGroup, {\n        \"data-slot\": \"dropdown-menu-radio-group\",\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n        lineNumber: 140,\n        columnNumber: 5\n    }, this);\n}\nfunction DropdownMenuRadioItem({ className, children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem, {\n        \"data-slot\": \"dropdown-menu-radio-item\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ChevronDownIcon_ChevronRightIcon_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"size-2 fill-current\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n                lineNumber: 157,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n        lineNumber: 149,\n        columnNumber: 5\n    }, this);\n}\nfunction DropdownMenuLabel({ className, inset, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        \"data-slot\": \"dropdown-menu-label\",\n        \"data-inset\": inset,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n        lineNumber: 169,\n        columnNumber: 5\n    }, this);\n}\nfunction DropdownMenuSeparator({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        \"data-slot\": \"dropdown-menu-separator\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-border -mx-1 my-1 h-px\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n        lineNumber: 183,\n        columnNumber: 5\n    }, this);\n}\nfunction DropdownMenuShortcut({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        \"data-slot\": \"dropdown-menu-shortcut\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground ml-auto text-xs tracking-widest\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n        lineNumber: 193,\n        columnNumber: 5\n    }, this);\n}\nfunction DropdownMenuSub({ ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Sub, {\n        \"data-slot\": \"dropdown-menu-sub\",\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n        lineNumber: 205,\n        columnNumber: 10\n    }, this);\n}\nfunction DropdownMenuSubTrigger({ className, inset, children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger, {\n        \"data-slot\": \"dropdown-menu-sub-trigger\",\n        \"data-inset\": inset,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ChevronDownIcon_ChevronRightIcon_CircleIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"ml-auto size-4\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n        lineNumber: 210,\n        columnNumber: 5\n    }, this);\n}\nfunction DropdownMenuSubContent({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent, {\n        \"data-slot\": \"dropdown-menu-sub-content\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"bg-white text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md p-1 shadow-lg\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\dropdown-menu.jsx\",\n        lineNumber: 227,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/dropdown-menu.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/error-boundary.jsx":
/*!**********************************************!*\
  !*** ./src/components/ui/error-boundary.jsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false,\n            error: null,\n            errorInfo: null\n        };\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            hasError: true\n        };\n    }\n    componentDidCatch(error, errorInfo) {\n        this.setState({\n            error: error,\n            errorInfo: errorInfo\n        });\n        // Log error to console in development\n        if (true) {\n            console.error('Error caught by boundary:', error, errorInfo);\n        }\n    }\n    render() {\n        if (this.state.hasError) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-md w-full bg-white shadow-lg rounded-lg p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center w-12 h-12 mx-auto bg-red-100 rounded-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6 text-red-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.jsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.jsx\",\n                                lineNumber: 33,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.jsx\",\n                            lineNumber: 32,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium text-gray-900\",\n                                    children: \"Something went wrong\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.jsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-2 text-sm text-gray-500\",\n                                    children: \"We're sorry, but something unexpected happened. Please try refreshing the page.\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.jsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.jsx\",\n                            lineNumber: 38,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>window.location.reload(),\n                                className: \"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n                                children: \"Refresh Page\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.jsx\",\n                                lineNumber: 48,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.jsx\",\n                            lineNumber: 47,\n                            columnNumber: 13\n                        }, this),\n                         true && this.state.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                            className: \"mt-4 text-left\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                                    className: \"cursor-pointer text-sm text-gray-600 hover:text-gray-800\",\n                                    children: \"Error Details (Development)\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.jsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-2 p-3 bg-gray-100 rounded text-xs font-mono overflow-auto\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-red-600 font-semibold mb-2\",\n                                            children: this.state.error.toString()\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.jsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-600\",\n                                            children: this.state.errorInfo.componentStack\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.jsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.jsx\",\n                                    lineNumber: 61,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.jsx\",\n                            lineNumber: 57,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.jsx\",\n                    lineNumber: 31,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\error-boundary.jsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, this);\n        }\n        return this.props.children;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ErrorBoundary);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/error-boundary.jsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/toast.jsx":
/*!*************************************!*\
  !*** ./src/components/ui/toast.jsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToastProvider: () => (/* binding */ ToastProvider),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   useToast: () => (/* binding */ useToast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ ToastProvider,useToast,default auto */ \n\n\n// Toast Context\nconst ToastContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\n// Toast Provider\nconst ToastProvider = ({ children })=>{\n    const [toasts, setToasts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const addToast = (message, type = \"error\", duration = 3000)=>{\n        const id = Date.now();\n        const newToast = {\n            id,\n            message,\n            type,\n            duration\n        };\n        setToasts((prev)=>[\n                ...prev,\n                newToast\n            ]);\n        // Auto remove toast after duration\n        setTimeout(()=>{\n            removeToast(id);\n        }, duration);\n    };\n    const removeToast = (id)=>{\n        setToasts((prev)=>prev.filter((toast)=>toast.id !== id));\n    };\n    const value = {\n        toasts,\n        addToast,\n        removeToast\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContext.Provider, {\n        value: value,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ToastContainer, {}, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, undefined);\n};\n// Toast Container\nconst ToastContainer = ()=>{\n    const { toasts, removeToast } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ToastContext);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed top-4 right-4 z-50 space-y-2\",\n        children: toasts.map((toast)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Toast, {\n                toast: toast,\n                onRemove: removeToast\n            }, toast.id, false, {\n                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n                lineNumber: 48,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, undefined);\n};\n// Individual Toast Component\nconst Toast = ({ toast, onRemove })=>{\n    const [isVisible, setIsVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Toast.useEffect\": ()=>{\n            // Animate in\n            setTimeout({\n                \"Toast.useEffect\": ()=>setIsVisible(true)\n            }[\"Toast.useEffect\"], 100);\n        }\n    }[\"Toast.useEffect\"], []);\n    const getToastStyles = (type)=>{\n        switch(type){\n            case \"error\":\n                return {\n                    bg: \"bg-red-50 border-red-200\",\n                    text: \"text-red-800\",\n                    icon: \"text-red-400\",\n                    iconBg: \"bg-red-100\",\n                    progress: \"bg-red-500\"\n                };\n            case \"success\":\n                return {\n                    bg: \"bg-green-50 border-green-200\",\n                    text: \"text-green-800\",\n                    icon: \"text-green-400\",\n                    iconBg: \"bg-green-100\",\n                    progress: \"bg-green-500\"\n                };\n            case \"warning\":\n                return {\n                    bg: \"bg-yellow-50 border-yellow-200\",\n                    text: \"text-yellow-800\",\n                    icon: \"text-yellow-400\",\n                    iconBg: \"bg-yellow-100\",\n                    progress: \"bg-yellow-500\"\n                };\n            case \"info\":\n                return {\n                    bg: \"bg-blue-50 border-blue-200\",\n                    text: \"text-blue-800\",\n                    icon: \"text-blue-400\",\n                    iconBg: \"bg-blue-100\",\n                    progress: \"bg-blue-500\"\n                };\n            default:\n                return {\n                    bg: \"bg-gray-50 border-gray-200\",\n                    text: \"text-gray-800\",\n                    icon: \"text-gray-400\",\n                    iconBg: \"bg-gray-100\",\n                    progress: \"bg-gray-500\"\n                };\n        }\n    };\n    const styles = getToastStyles(toast.type);\n    const getIcon = (type)=>{\n        switch(type){\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n                        lineNumber: 115,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n                    lineNumber: 114,\n                    columnNumber: 11\n                }, undefined);\n            case \"success\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n                        lineNumber: 125,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n                    lineNumber: 124,\n                    columnNumber: 11\n                }, undefined);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n                        lineNumber: 135,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n                    lineNumber: 134,\n                    columnNumber: 11\n                }, undefined);\n            case \"info\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n                        lineNumber: 145,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n                    lineNumber: 144,\n                    columnNumber: 11\n                }, undefined);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n                        lineNumber: 155,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n                    lineNumber: 154,\n                    columnNumber: 11\n                }, undefined);\n        }\n    };\n    const handleRemove = ()=>{\n        setIsVisible(false);\n        setTimeout(()=>onRemove(toast.id), 300);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `\n        transform transition-all duration-300 ease-in-out\n        ${isVisible ? \"translate-x-0 opacity-100\" : \"translate-x-full opacity-0\"}\n        max-w-lg w-full bg-white shadow-lg rounded-lg pointer-events-auto\n        border ${styles.bg} overflow-hidden\n      `,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `flex-shrink-0 rounded-full p-1 ${styles.iconBg}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `${styles.icon}`,\n                                children: getIcon(toast.type)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n                            lineNumber: 183,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-3 flex-1 min-w-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: `text-sm font-medium break-words ${styles.text}`,\n                                style: {\n                                    wordBreak: \"break-word\"\n                                },\n                                children: toast.message\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"ml-4 flex-shrink-0 flex\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleRemove,\n                                className: `inline-flex ${styles.text} hover:${styles.text.replace(\"text-\", \"text-\")} focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500`,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"sr-only\",\n                                        children: \"Close\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"h-5 w-5\",\n                                        viewBox: \"0 0 20 20\",\n                                        fill: \"currentColor\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n                    lineNumber: 182,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n                lineNumber: 181,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-1 bg-gray-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `h-full ${styles.progress} transition-all duration-300 ease-linear`,\n                    style: {\n                        width: \"100%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n                    lineNumber: 219,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\ui\\\\toast.jsx\",\n        lineNumber: 171,\n        columnNumber: 5\n    }, undefined);\n};\n// Custom hook to use toast\nconst useToast = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ToastContext);\n    if (!context) {\n        throw new Error(\"useToast must be used within a ToastProvider\");\n    }\n    return context;\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ToastProvider);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/toast.jsx\n");

/***/ }),

/***/ "(ssr)/./src/data/dashboard.js":
/*!*******************************!*\
  !*** ./src/data/dashboard.js ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   monthToFile: () => (/* binding */ monthToFile)\n/* harmony export */ });\nconst monthToFile = {\n    May: \"/uploads/dashboard_2.pdf\",\n    June: \"/uploads/June.pdf\",\n    July: \"/uploads/July.pdf\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvZGF0YS9kYXNoYm9hcmQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPLE1BQU1BLGNBQWM7SUFDekJDLEtBQUs7SUFDTEMsTUFBTTtJQUNOQyxNQUFNO0FBQ1IsRUFBRSIsInNvdXJjZXMiOlsiRDpcXFByb2plY3RzXFxDcGEtZGFzaGJvYXJkXFxjcGEtZGFzaGJvYXJkXFxjcGEtZGFzaGJvYXJkXFxmcm9udGVuZFxcc3JjXFxkYXRhXFxkYXNoYm9hcmQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IG1vbnRoVG9GaWxlID0ge1xyXG4gIE1heTogXCIvdXBsb2Fkcy9kYXNoYm9hcmRfMi5wZGZcIixcclxuICBKdW5lOiBcIi91cGxvYWRzL0p1bmUucGRmXCIsXHJcbiAgSnVseTogXCIvdXBsb2Fkcy9KdWx5LnBkZlwiLFxyXG59O1xyXG4iXSwibmFtZXMiOlsibW9udGhUb0ZpbGUiLCJNYXkiLCJKdW5lIiwiSnVseSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/data/dashboard.js\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useAuth.js":
/*!******************************!*\
  !*** ./src/hooks/useAuth.js ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_api_auth_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/auth.js */ \"(ssr)/./src/lib/api/auth.js\");\n/* harmony import */ var _lib_tokenStorage_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/tokenStorage.js */ \"(ssr)/./src/lib/tokenStorage.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\n\n// Create Auth Context\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\n// Auth Provider Component\nconst AuthProvider = ({ children })=>{\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Check if user is authenticated on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            checkAuth();\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const checkAuth = async ()=>{\n        try {\n            const authData = _lib_tokenStorage_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getAuthData();\n            if (authData.accessToken && authData.user && !_lib_tokenStorage_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].isTokenExpired(authData.accessToken)) {\n                setUser(authData.user);\n            } else {\n                // Token expired or no user data, try to refresh\n                if (authData.refreshToken && !_lib_tokenStorage_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].isTokenExpired(authData.refreshToken)) {\n                    try {\n                        const response = await _lib_api_auth_js__WEBPACK_IMPORTED_MODULE_3__.authAPI.refreshToken();\n                        const { accessToken, refreshToken, user: userData } = response.data;\n                        _lib_tokenStorage_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].setAuthData({\n                            accessToken,\n                            refreshToken,\n                            user: userData\n                        });\n                        setUser(userData);\n                    } catch (refreshError) {\n                        console.error('Token refresh failed:', refreshError);\n                        _lib_tokenStorage_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].clearAuthData();\n                    }\n                } else {\n                    _lib_tokenStorage_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].clearAuthData();\n                }\n            }\n        } catch (error) {\n            console.error('Auth check failed:', error);\n            _lib_tokenStorage_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].clearAuthData();\n        } finally{\n            setLoading(false);\n        }\n    };\n    const login = async (credentials)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await _lib_api_auth_js__WEBPACK_IMPORTED_MODULE_3__.authAPI.login(credentials);\n            // Store tokens and user data\n            const { accessToken, refreshToken, user: userData } = response.data;\n            _lib_tokenStorage_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].setAuthData({\n                accessToken,\n                refreshToken,\n                user: userData\n            });\n            setUser(userData);\n            // Redirect based on user role description\n            if (userData.role?.description === 'Super Admin') {\n                router.push('/listing');\n            } else {\n                router.push('/dashboard');\n            }\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            let errorMessage = 'Login failed';\n            if (error.response) {\n                const { status, data } = error.response;\n                switch(status){\n                    case 400:\n                        errorMessage = data.message || 'Invalid credentials';\n                        break;\n                    case 401:\n                        errorMessage = data.message || 'Invalid email or password';\n                        break;\n                    case 403:\n                        errorMessage = data.message || 'Account is deactivated';\n                        break;\n                    case 429:\n                        errorMessage = data.message || 'Too many login attempts. Please try again later.';\n                        break;\n                    case 500:\n                        errorMessage = 'Server error. Please try again later.';\n                        break;\n                    default:\n                        errorMessage = data.message || 'Login failed';\n                }\n            } else if (error.request) {\n                errorMessage = 'Network error. Please check your connection.';\n            } else {\n                errorMessage = error.message || 'Login failed';\n            }\n            setError(errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signup = async (userData)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await _lib_api_auth_js__WEBPACK_IMPORTED_MODULE_3__.authAPI.signup(userData);\n            // Store refresh and reset tokens from signup\n            const { refreshToken, resetToken, newUser } = response.data;\n            _lib_tokenStorage_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].setAuthData({\n                refreshToken,\n                resetToken,\n                user: newUser\n            });\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            const errorMessage = error.response?.data?.message || 'Signup failed';\n            setError(errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    const logout = async ()=>{\n        try {\n            await _lib_api_auth_js__WEBPACK_IMPORTED_MODULE_3__.authAPI.logout();\n        } catch (error) {\n            console.error('Logout error:', error);\n        } finally{\n            // Clear all auth data\n            _lib_tokenStorage_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].clearAuthData();\n            setUser(null);\n            router.push('/login');\n        }\n    };\n    const forgotPassword = async (email, url)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await _lib_api_auth_js__WEBPACK_IMPORTED_MODULE_3__.authAPI.forgotPassword(email, url);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            const errorMessage = error.response?.data?.message || 'Failed to send reset email';\n            setError(errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    const resetPassword = async (token, newPassword, confirmPassword)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await _lib_api_auth_js__WEBPACK_IMPORTED_MODULE_3__.authAPI.resetPassword(token, newPassword, confirmPassword);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            const errorMessage = error.response?.data?.message || 'Password reset failed';\n            setError(errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    const changePassword = async (oldPassword, newPassword, confirmPassword)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await _lib_api_auth_js__WEBPACK_IMPORTED_MODULE_3__.authAPI.changePassword(oldPassword, newPassword, confirmPassword);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            const errorMessage = error.response?.data?.message || 'Password change failed';\n            setError(errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    const updateProfile = async (userId, userData)=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await _lib_api_auth_js__WEBPACK_IMPORTED_MODULE_3__.authAPI.updateProfile(userId, userData);\n            // Update local user state and storage\n            setUser(response.data);\n            _lib_tokenStorage_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].setUserData(response.data);\n            return {\n                success: true,\n                data: response.data\n            };\n        } catch (error) {\n            const errorMessage = error.response?.data?.message || 'Profile update failed';\n            setError(errorMessage);\n            return {\n                success: false,\n                error: errorMessage\n            };\n        } finally{\n            setLoading(false);\n        }\n    };\n    const clearError = ()=>{\n        setError(null);\n    };\n    const value = {\n        user,\n        loading,\n        error,\n        login,\n        signup,\n        logout,\n        forgotPassword,\n        resetPassword,\n        changePassword,\n        updateProfile,\n        clearError,\n        isAuthenticated: !!user,\n        getUserRole: ()=>_lib_tokenStorage_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getUserRole()\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\hooks\\\\useAuth.js\",\n        lineNumber: 238,\n        columnNumber: 10\n    }, undefined);\n};\n// Custom hook to use auth context\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!context) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useAuth.js\n");

/***/ }),

/***/ "(ssr)/./src/lib/api/auth.js":
/*!*****************************!*\
  !*** ./src/lib/api/auth.js ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   authAPI: () => (/* binding */ authAPI)\n/* harmony export */ });\n/* harmony import */ var _axios_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../axios.js */ \"(ssr)/./src/lib/axios.js\");\n\n// Auth API service functions\nconst authAPI = {\n    // Register a new user\n    signup: async (userData)=>{\n        const response = await _axios_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/auth/signup\", userData);\n        return response.data;\n    },\n    // Login user\n    login: async (credentials)=>{\n        const response = await _axios_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/auth/login\", credentials);\n        return response.data;\n    },\n    // Forgot password\n    forgotPassword: async (email, url)=>{\n        const response = await _axios_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/auth/forgot-password\", {\n            email,\n            url\n        });\n        return response.data;\n    },\n    // Reset password\n    resetPassword: async (token, newPassword, confirmPassword)=>{\n        const response = await _axios_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/auth/reset-password\", {\n            token,\n            newPassword,\n            confirmPassword\n        });\n        return response.data;\n    },\n    // Change password\n    changePassword: async (oldPassword, newPassword, confirmPassword)=>{\n        const response = await _axios_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/auth/change-password\", {\n            oldPassword,\n            newPassword,\n            confirmPassword\n        });\n        return response.data;\n    },\n    // Get user profile\n    getProfile: async ()=>{\n        const response = await _axios_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/auth/me\");\n        return response.data;\n    },\n    // Update user profile\n    updateProfile: async (userId, userData)=>{\n        const response = await _axios_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(`/auth/profile/${userId}`, userData);\n        return response.data;\n    },\n    // Logout user\n    logout: async ()=>{\n        const response = await _axios_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/auth/logout\");\n        return response.data;\n    },\n    // Refresh token\n    refreshToken: async ()=>{\n        const response = await _axios_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/auth/refresh-token\");\n        return response.data;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/auth.js\n");

/***/ }),

/***/ "(ssr)/./src/lib/axios.js":
/*!**************************!*\
  !*** ./src/lib/axios.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _tokenStorage_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./tokenStorage.js */ \"(ssr)/./src/lib/tokenStorage.js\");\n\n\n// Create axios instance\nconst api = axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"].create({\n    baseURL: \"http://localhost:3003/api/v1\",\n    timeout: 10000,\n    withCredentials: true,\n    headers: {\n        'Content-Type': 'application/json'\n    }\n});\n// Request interceptor\napi.interceptors.request.use((config)=>{\n    const accessToken = _tokenStorage_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getAccessToken();\n    if (accessToken && !_tokenStorage_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].isTokenExpired(accessToken)) {\n        config.headers.Authorization = `Bearer ${accessToken}`;\n    }\n    return config;\n}, (error)=>{\n    return Promise.reject(error);\n});\n// Response interceptor for handling errors\napi.interceptors.response.use((response)=>response, (error)=>{\n    if (error.response?.status === 401) {\n        // Unauthorized - clear auth data and redirect to login\n        _tokenStorage_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].clearAuthData();\n        if (false) {}\n    } else if (error.response?.status === 403) {\n        // Forbidden - clear auth data and redirect to login\n        _tokenStorage_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].clearAuthData();\n        if (false) {}\n    }\n    return Promise.reject(error);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (api);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/axios.js\n");

/***/ }),

/***/ "(ssr)/./src/lib/tokenStorage.js":
/*!*********************************!*\
  !*** ./src/lib/tokenStorage.js ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   tokenStorage: () => (/* binding */ tokenStorage)\n/* harmony export */ });\n// Token storage keys\nconst STORAGE_KEYS = {\n    ACCESS_TOKEN: 'accessToken',\n    REFRESH_TOKEN: 'refreshToken',\n    USER_DATA: 'userData',\n    RESET_TOKEN: 'resetToken'\n};\n/**\r\n * Check if localStorage is available (client-side only)\r\n * @returns {boolean} True if localStorage is available\r\n */ const isLocalStorageAvailable = ()=>{\n    return  false && 0;\n};\n/**\r\n * Token storage utility for localStorage operations\r\n */ const tokenStorage = {\n    /**\r\n   * Store access token in localStorage\r\n   * @param {string} token - Access token\r\n   */ setAccessToken: (token)=>{\n        try {\n            if (isLocalStorageAvailable()) {\n                localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, token);\n            }\n        } catch (error) {\n            console.error('Error storing access token:', error);\n        }\n    },\n    /**\r\n   * Get access token from localStorage\r\n   * @returns {string|null} Access token or null\r\n   */ getAccessToken: ()=>{\n        try {\n            if (isLocalStorageAvailable()) {\n                return localStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);\n            }\n            return null;\n        } catch (error) {\n            console.error('Error getting access token:', error);\n            return null;\n        }\n    },\n    /**\r\n   * Store refresh token in localStorage\r\n   * @param {string} token - Refresh token\r\n   */ setRefreshToken: (token)=>{\n        try {\n            if (isLocalStorageAvailable()) {\n                localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, token);\n            }\n        } catch (error) {\n            console.error('Error storing refresh token:', error);\n        }\n    },\n    /**\r\n   * Get refresh token from localStorage\r\n   * @returns {string|null} Refresh token or null\r\n   */ getRefreshToken: ()=>{\n        try {\n            if (isLocalStorageAvailable()) {\n                return localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);\n            }\n            return null;\n        } catch (error) {\n            console.error('Error getting refresh token:', error);\n            return null;\n        }\n    },\n    /**\r\n   * Store reset token in localStorage\r\n   * @param {string} token - Reset token\r\n   */ setResetToken: (token)=>{\n        try {\n            if (isLocalStorageAvailable()) {\n                localStorage.setItem(STORAGE_KEYS.RESET_TOKEN, token);\n            }\n        } catch (error) {\n            console.error('Error storing reset token:', error);\n        }\n    },\n    /**\r\n   * Get reset token from localStorage\r\n   * @returns {string|null} Reset token or null\r\n   */ getResetToken: ()=>{\n        try {\n            if (isLocalStorageAvailable()) {\n                return localStorage.getItem(STORAGE_KEYS.RESET_TOKEN);\n            }\n            return null;\n        } catch (error) {\n            console.error('Error getting reset token:', error);\n            return null;\n        }\n    },\n    /**\r\n   * Store user data in localStorage\r\n   * @param {Object} userData - User data object\r\n   */ setUserData: (userData)=>{\n        try {\n            if (isLocalStorageAvailable()) {\n                localStorage.setItem(STORAGE_KEYS.USER_DATA, JSON.stringify(userData));\n            }\n        } catch (error) {\n            console.error('Error storing user data:', error);\n        }\n    },\n    /**\r\n   * Get user data from localStorage\r\n   * @returns {Object|null} User data object or null\r\n   */ getUserData: ()=>{\n        try {\n            if (isLocalStorageAvailable()) {\n                const userData = localStorage.getItem(STORAGE_KEYS.USER_DATA);\n                return userData ? JSON.parse(userData) : null;\n            }\n            return null;\n        } catch (error) {\n            console.error('Error getting user data:', error);\n            return null;\n        }\n    },\n    /**\r\n   * Store all authentication data\r\n   * @param {Object} authData - Authentication data object\r\n   */ setAuthData: (authData)=>{\n        const { accessToken, refreshToken, user, resetToken } = authData;\n        if (accessToken) {\n            tokenStorage.setAccessToken(accessToken);\n        }\n        if (refreshToken) {\n            tokenStorage.setRefreshToken(refreshToken);\n        }\n        if (resetToken) {\n            tokenStorage.setResetToken(resetToken);\n        }\n        if (user) {\n            tokenStorage.setUserData(user);\n        }\n    },\n    /**\r\n   * Get all authentication data\r\n   * @returns {Object} Authentication data object\r\n   */ getAuthData: ()=>{\n        return {\n            accessToken: tokenStorage.getAccessToken(),\n            refreshToken: tokenStorage.getRefreshToken(),\n            resetToken: tokenStorage.getResetToken(),\n            user: tokenStorage.getUserData()\n        };\n    },\n    /**\r\n   * Clear all authentication data from localStorage\r\n   */ clearAuthData: ()=>{\n        try {\n            if (isLocalStorageAvailable()) {\n                localStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN);\n                localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);\n                localStorage.removeItem(STORAGE_KEYS.USER_DATA);\n                localStorage.removeItem(STORAGE_KEYS.RESET_TOKEN);\n            }\n        } catch (error) {\n            console.error('Error clearing auth data:', error);\n        }\n    },\n    /**\r\n   * Check if user is authenticated\r\n   * @returns {boolean} True if user is authenticated\r\n   */ isAuthenticated: ()=>{\n        const accessToken = tokenStorage.getAccessToken();\n        const user = tokenStorage.getUserData();\n        return !!(accessToken && user);\n    },\n    /**\r\n   * Get user role\r\n   * @returns {string|null} User role or null\r\n   */ getUserRole: ()=>{\n        const user = tokenStorage.getUserData();\n        return user?.role?.name || null;\n    },\n    /**\r\n   * Check if token is expired\r\n   * @param {string} token - JWT token\r\n   * @returns {boolean} True if token is expired\r\n   */ isTokenExpired: (token)=>{\n        if (!token) return true;\n        try {\n            const payload = JSON.parse(atob(token.split('.')[1]));\n            const currentTime = Date.now() / 1000;\n            return payload.exp < currentTime;\n        } catch (error) {\n            console.error('Error checking token expiration:', error);\n            return true;\n        }\n    },\n    /**\r\n   * Check if access token is expired\r\n   * @returns {boolean} True if access token is expired\r\n   */ isAccessTokenExpired: ()=>{\n        const accessToken = tokenStorage.getAccessToken();\n        return tokenStorage.isTokenExpired(accessToken);\n    },\n    /**\r\n   * Check if refresh token is expired\r\n   * @returns {boolean} True if refresh token is expired\r\n   */ isRefreshTokenExpired: ()=>{\n        const refreshToken = tokenStorage.getRefreshToken();\n        return tokenStorage.isTokenExpired(refreshToken);\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (tokenStorage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/tokenStorage.js\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.js":
/*!**************************!*\
  !*** ./src/lib/utils.js ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   getInitials: () => (/* binding */ getInitials)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nconst getInitials = (name)=>{\n    return name.split(\" \").map((n)=>n[0]).join(\"\").toUpperCase();\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBNEI7QUFDWTtBQUVqQyxTQUFTRSxHQUFHLEdBQUdDLE1BQU07SUFDMUIsT0FBT0YsdURBQU9BLENBQUNELDBDQUFJQSxDQUFDRztBQUN0QjtBQUVPLE1BQU1DLGNBQWMsQ0FBQ0M7SUFDeEIsT0FBT0EsS0FDSkMsS0FBSyxDQUFDLEtBQ05DLEdBQUcsQ0FBQyxDQUFDQyxJQUFNQSxDQUFDLENBQUMsRUFBRSxFQUNmQyxJQUFJLENBQUMsSUFDTEMsV0FBVztBQUNoQixFQUFFIiwic291cmNlcyI6WyJEOlxcUHJvamVjdHNcXENwYS1kYXNoYm9hcmRcXGNwYS1kYXNoYm9hcmRcXGNwYS1kYXNoYm9hcmRcXGZyb250ZW5kXFxzcmNcXGxpYlxcdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY2xzeCB9IGZyb20gXCJjbHN4XCI7XHJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxyXG5cclxuZXhwb3J0IGZ1bmN0aW9uIGNuKC4uLmlucHV0cykge1xyXG4gIHJldHVybiB0d01lcmdlKGNsc3goaW5wdXRzKSk7XHJcbn1cclxuXHJcbmV4cG9ydCBjb25zdCBnZXRJbml0aWFscyA9IChuYW1lKSA9PiB7XHJcbiAgICByZXR1cm4gbmFtZVxyXG4gICAgICAuc3BsaXQoXCIgXCIpXHJcbiAgICAgIC5tYXAoKG4pID0+IG5bMF0pXHJcbiAgICAgIC5qb2luKFwiXCIpXHJcbiAgICAgIC50b1VwcGVyQ2FzZSgpO1xyXG4gIH07Il0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiLCJnZXRJbml0aWFscyIsIm5hbWUiLCJzcGxpdCIsIm1hcCIsIm4iLCJqb2luIiwidG9VcHBlckNhc2UiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.js\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/dynamic-access-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/server/app-render/dynamic-access-async-storage.external.js" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/dynamic-access-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "canvas":
/*!*************************!*\
  !*** external "canvas" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("canvas");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/follow-redirects","vendor-chunks/debug","vendor-chunks/form-data","vendor-chunks/get-intrinsic","vendor-chunks/asynckit","vendor-chunks/combined-stream","vendor-chunks/mime-types","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/supports-color","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/function-bind","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/has-flag","vendor-chunks/gopd","vendor-chunks/es-define-property","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/@floating-ui","vendor-chunks/tslib","vendor-chunks/react-remove-scroll","vendor-chunks/lucide-react","vendor-chunks/aria-hidden","vendor-chunks/react-remove-scroll-bar","vendor-chunks/use-callback-ref","vendor-chunks/use-sidecar","vendor-chunks/class-variance-authority","vendor-chunks/react-style-singleton","vendor-chunks/clsx","vendor-chunks/get-nonce","vendor-chunks/pdfjs-dist"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(dashboard)%2Fdashboard%2Fpage&page=%2F(dashboard)%2Fdashboard%2Fpage&appPaths=%2F(dashboard)%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2F(dashboard)%2Fdashboard%2Fpage.js&appDir=D%3A%5CProjects%5CCpa-dashboard%5Ccpa-dashboard%5Ccpa-dashboard%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CProjects%5CCpa-dashboard%5Ccpa-dashboard%5Ccpa-dashboard%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();