import React from "react";
import { Users, User<PERSON>he<PERSON>, Clock, TrendingUp } from "lucide-react";

// Sample data to match your image
const dashboardStats = [
  {
    title: "Total Clients",
    value: "142",
    icon: "users",
    bgColor: "bg-purple-100",
    iconColor: "text-purple-600",
  },
  {
    title: "Active Clients",
    value: "128",
    icon: "user-check",
    bgColor: "bg-green-100",
    iconColor: "text-green-600",
  },
  {
    title: "Pending Updates",
    value: "23",
    icon: "clock",
    bgColor: "bg-yellow-100",
    iconColor: "text-yellow-600",
  },
  {
    title: "This Month",
    value: "89",
    icon: "trending-up",
    bgColor: "bg-red-100",
    iconColor: "text-red-600",
  },
];

const iconMap = {
  users: <Users className="w-6 h-6" />,
  "user-check": <UserCheck className="w-6 h-6" />,
  clock: <Clock className="w-6 h-6" />,
  "trending-up": <TrendingUp className="w-6 h-6" />,
};

export default function StatsCards() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
      {dashboardStats.map((stat, index) => (
        <div
          key={index}
          className="bg-white rounded-xl p-4 shadow-md border border-gray-100 transition-all duration-300 hover:shadow-2xl hover:scale-102"
        >
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-600 mb-1">
                {stat.title}
              </p>
              <p className="text-2xl font-bold text-gray-900 leading-tight">
                {stat.value}
              </p>
            </div>
            <div
              className={`p-2 rounded-full ${stat.bgColor} ${stat.iconColor} ml-3 flex-shrink-0`}
            >
              {iconMap[stat.icon]}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}
