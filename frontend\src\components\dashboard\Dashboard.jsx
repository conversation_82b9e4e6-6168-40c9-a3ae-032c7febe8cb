import React, { useEffect, useState } from "react";
import { Button } from "../ui/button";
import { ArrowLeft } from "lucide-react";
import { useRouter } from "next/navigation";
import PdfViewer from "@/components/dashboard/PDFViewer";
import tokenStorage from "@/lib/tokenStorage";
import { monthToFile } from "@/data/dashboard";
import { DropdownMenu } from "../ui/dropdown-menu";

export default function Dashboard() {
  const router = useRouter();
  const [months, setMonths] = useState("July");
  const [isUserAdmin, setIsUserAdmin] = useState(false);

  useEffect(() => {
    const userData = tokenStorage.getUserData();
    setIsUserAdmin(userData?.role?.name === "admin");
  }, []);

  const handleBack = () => {
    router.push("/listing");
  };

  const pdfPath = monthToFile[months] || "";

  if (!pdfPath) {
    return (
      <div className="flex items-center justify-center h-64 text-gray-500">
        No PDF file provided.
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-4 gap-4">
        {isUserAdmin ? (
          <Button
            variant="outline"
            leftIcon={<ArrowLeft />}
            onClick={() => handleBack()}
          >
            Back to Listing
          </Button>
        ) : (
          <div></div>
        )}
        <div className="flex items-center gap-2">
          <DropdownMenu
            selected={months}
            onSelect={setMonths}
            options={["May", "June", "July"]}
            customHeight="40px"
          />
          <Button variant="default">Download Dashboard</Button>
        </div>
      </div>
      <PdfViewer url={pdfPath} />
    </div>
  );
}
