"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/dashboard/PDFViewer.jsx":
/*!************************************************!*\
  !*** ./src/components/dashboard/PDFViewer.jsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var pdfjs_dist__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! pdfjs-dist */ \"(app-pages-browser)/./node_modules/pdfjs-dist/build/pdf.js\");\n/* harmony import */ var pdfjs_dist__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(pdfjs_dist__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var pdfjs_dist_build_pdf_worker_min_js_url__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! pdfjs-dist/build/pdf.worker.min.js?url */ \"(app-pages-browser)/./node_modules/pdfjs-dist/build/pdf.worker.min.js?url\");\n/* harmony import */ var pdfjs_dist_build_pdf_worker_min_js_url__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(pdfjs_dist_build_pdf_worker_min_js_url__WEBPACK_IMPORTED_MODULE_3__);\n// components/PdfCanvasViewer.js\n\nvar _s = $RefreshSig$();\n\n\n\npdfjs_dist__WEBPACK_IMPORTED_MODULE_2__.GlobalWorkerOptions.workerSrc = (pdfjs_dist_build_pdf_worker_min_js_url__WEBPACK_IMPORTED_MODULE_3___default());\nconst PdfCanvasViewer = (param)=>{\n    let { url } = param;\n    _s();\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const renderTaskRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PdfCanvasViewer.useEffect\": ()=>{\n            const renderPdf = {\n                \"PdfCanvasViewer.useEffect.renderPdf\": async ()=>{\n                    try {\n                        const loadingTask = pdfjs_dist__WEBPACK_IMPORTED_MODULE_2__.getDocument(url);\n                        const pdf = await loadingTask.promise;\n                        const page = await pdf.getPage(1);\n                        const desiredWidth = window.innerWidth * 0.98;\n                        const viewport = page.getViewport({\n                            scale: 1\n                        });\n                        const scale = desiredWidth / viewport.width;\n                        const scaledViewport = page.getViewport({\n                            scale\n                        });\n                        const canvas = canvasRef.current;\n                        const context = canvas.getContext(\"2d\");\n                        // High-DPI screens for sharp rendering\n                        const dpr = window.devicePixelRatio || 1;\n                        canvas.width = scaledViewport.width * dpr;\n                        canvas.height = scaledViewport.height * dpr;\n                        canvas.style.width = \"\".concat(scaledViewport.width, \"px\");\n                        canvas.style.height = \"\".concat(scaledViewport.height, \"px\");\n                        context.setTransform(dpr, 0, 0, dpr, 0, 0);\n                        if (renderTaskRef.current) {\n                            renderTaskRef.current.cancel();\n                        }\n                        renderTaskRef.current = page.render({\n                            canvasContext: context,\n                            viewport: scaledViewport\n                        });\n                        await renderTaskRef.current.promise;\n                    } catch (err) {\n                        if ((err === null || err === void 0 ? void 0 : err.name) !== \"RenderingCancelledException\") {\n                            console.error(\"PDF render error:\", err);\n                        }\n                    }\n                }\n            }[\"PdfCanvasViewer.useEffect.renderPdf\"];\n            renderPdf();\n            return ({\n                \"PdfCanvasViewer.useEffect\": ()=>{\n                    if (renderTaskRef.current) {\n                        renderTaskRef.current.cancel();\n                    }\n                }\n            })[\"PdfCanvasViewer.useEffect\"];\n        }\n    }[\"PdfCanvasViewer.useEffect\"], [\n        url\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n        ref: canvasRef,\n        style: {\n            display: \"block\",\n            width: \"98vw\",\n            height: \"auto\",\n            margin: 0,\n            padding: 0,\n            background: \"none\",\n            boxShadow: \"none\",\n            border: \"none\"\n        }\n    }, void 0, false, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\dashboard\\\\PDFViewer.jsx\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PdfCanvasViewer, \"jqa9j7J8Ww3vd8rHYPtA54p8Zu8=\");\n_c = PdfCanvasViewer;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PdfCanvasViewer);\nvar _c;\n$RefreshReg$(_c, \"PdfCanvasViewer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/dashboard/PDFViewer.jsx\n"));

/***/ })

});