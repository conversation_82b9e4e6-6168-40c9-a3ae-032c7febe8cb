"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "../ui/button";
import { Timer } from "lucide-react";
import { clientsData } from "@/data/clients";
import { getInitials } from "@/lib/utils";

export default function ClientTable({
  searchTerm = "",
  statusFilter = "All Status",
}) {
  const [currentPage, setCurrentPage] = useState(1);
  const [hoveredRow, setHoveredRow] = useState(null);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: "asc" });
  const itemsPerPage = 6;

  const router = useRouter();

  const filteredClients = clientsData.filter((client) => {
    const matchesSearch =
      client.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      client.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      client.company.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === "All Status" || client.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  // Sort functionality
  const sortedClients = [...filteredClients].sort((a, b) => {
    if (!sortConfig.key) return 0;

    const aVal = a[sortConfig.key].toLowerCase();
    const bVal = b[sortConfig.key].toLowerCase();

    if (sortConfig.direction === "asc") {
      return aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
    } else {
      return aVal > bVal ? -1 : aVal < bVal ? 1 : 0;
    }
  });

  const totalPages = Math.ceil(sortedClients.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedClients = sortedClients.slice(
    startIndex,
    startIndex + itemsPerPage
  );

  const handleSort = (key) => {
    setSortConfig((prev) => ({
      key,
      direction: prev.key === key && prev.direction === "asc" ? "desc" : "asc",
    }));
  };

  const getSortIcon = (columnKey) => {
    if (sortConfig.key !== columnKey) {
      return (
        <svg
          className="w-4 h-4 ml-1 text-gray-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M8 9l4-4 4 4m0 6l-4 4-4-4"
          />
        </svg>
      );
    }

    return sortConfig.direction === "asc" ? (
      <svg
        className="w-4 h-4 ml-1 text-blue-600"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M5 15l7-7 7 7"
        />
      </svg>
    ) : (
      <svg
        className="w-4 h-4 ml-1 text-blue-600"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M19 9l-7 7-7-7"
        />
      </svg>
    );
  };

  const getStatusBadge = (status) => {
    const baseClasses =
      "px-3 py-1.5 rounded-full text-xs font-semibold transition-all duration-200 transform hover:scale-105";
    if (status === "Active") {
      return `${baseClasses} bg-green-100 text-green-700 border border-green-300`;
    }
    return `${baseClasses} bg-red-100 text-red-700 border border-red-300`;
  };

  const handleViewDetails = (clientId) => {
    router.push("/dashboard");
  };

  const handleBookkeeping = (clientId) => {
    router.push(`/book-closure/${clientId}`);
  };

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden">
      {/* Header */}
      <div className="px-4 py-3 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-gray-900 font-semibold text-lg">
                {filteredClients.length} clients
              </span>
            </div>
            <div className="flex bg-green-100 items-center space-x-2 text-green-600 text-md font-medium p-1 rounded-md">
              <Timer />
              Updated 14 minutes ago
            </div>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full table-fixed align-middle">
          <colgroup>
            <col style={{ width: "25%" }} />
            <col style={{ width: "20%" }} />
            <col style={{ width: "15%" }} />
            <col style={{ width: "15%" }} />
            <col style={{ width: "20%" }} />
          </colgroup>
          <thead>
            <tr className="border-b border-gray-200 bg-gray-100">
              <th
                className="px-4 py-4 text-left text-md font-semibold text-gray-700 cursor-pointer hover:text-blue-600 transition-colors duration-200"
                onClick={() => handleSort("name")}
              >
                <div className="flex items-center">
                  Client Name
                  {getSortIcon("name")}
                </div>
              </th>
              <th
                className="px-4 py-4 text-left text-md font-semibold text-gray-700 cursor-pointer hover:text-blue-600 transition-colors duration-200"
                onClick={() => handleSort("company")}
              >
                <div className="flex items-center">
                  Company Name
                  {getSortIcon("company")}
                </div>
              </th>
              <th className="px-2 py-4 text-left text-md font-semibold text-gray-700">
                Status
              </th>
              <th
                className="px-4 py-4 text-left text-md font-semibold text-gray-700 cursor-pointer hover:text-blue-600 transition-colors duration-200"
                onClick={() => handleSort("lastBookkeeping")}
              >
                <div className="flex items-center">
                  Last Booking
                  {getSortIcon("lastBookkeeping")}
                </div>
              </th>
              <th className="px-4 py-4 text-left text-md font-semibold text-gray-700">
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {paginatedClients.map((client, index) => (
              <tr
                key={client.id}
                className={`transition-all duration-200 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:shadow-sm ${
                  index !== paginatedClients.length - 1
                    ? "border-b border-gray-50"
                    : ""
                }`}
                onMouseEnter={() => setHoveredRow(client.id)}
                onMouseLeave={() => setHoveredRow(null)}
              >
                <td className="px-3 py-2 min-w-0">
                  <div className="flex items-center group min-w-0">
                    <div
                      className={`w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-600 rounded-xl flex items-center justify-center transition-all duration-300 ${
                        hoveredRow === client.id
                          ? "transform scale-110 shadow-lg"
                          : ""
                      }`}
                    >
                      <span className="text-white text-md font-bold">
                        {getInitials(client.name)}
                      </span>
                    </div>
                    <div className="ml-4 min-w-0">
                      <div className="text-md font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 truncate">
                        {client.name}
                      </div>
                      <div className="text-md text-gray-500 group-hover:text-gray-700 transition-colors duration-200 truncate">
                        {client.email}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="px-3 py-2 min-w-0">
                  <div className="text-md text-green-700">
                    {client.company}
                  </div>
                </td>
                <td className="px-2 py-2">
                  <span className={getStatusBadge(client.status)}>
                    {client.status}
                  </span>
                </td>
                <td className="px-3 py-2">
                  <div className="text-md text-green-700">
                    {new Date(client.lastBookkeeping).toLocaleDateString(
                      "en-US",
                      {
                        month: "short",
                        day: "numeric",
                        year: "numeric",
                      }
                    )}
                  </div>
                </td>
                <td className="px-3 py-2">
                  <div
                    className={`flex items-center space-x-2 transition-all duration-300 ${
                      hoveredRow === client.id ? "transform translate-x-1" : ""
                    }`}
                  >
                    <Button
                      variant="outline"
                      className="py-2 px-3"
                      onClick={() => handleViewDetails(client.id)}
                    >
                      View Details
                    </Button>
                    <Button
                      className="py-2 px-3"
                      onClick={() => handleBookkeeping(client.id)}
                    >
                      Submit Book-closure
                    </Button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <div className="px-4 py-2 border-t border-gray-100 bg-gradient-to-r from-gray-50 to-gray-100">
        <div className="flex items-center justify-between">
          <div className="text-md text-gray-600 font-medium">
            Showing {startIndex + 1} to{" "}
            {Math.min(startIndex + itemsPerPage, filteredClients.length)} of{" "}
            {filteredClients.length} results
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 rounded-lg hover:bg-white disabled:hover:bg-transparent transition-all duration-200 hover:shadow-md"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>

            {[1, 2, 3].map((page) => (
              <button
                key={page}
                onClick={() => setCurrentPage(page)}
                className={`px-4 py-2 rounded-lg text-md font-semibold transition-all duration-200 hover:shadow-md ${
                  currentPage === page
                    ? "bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-lg transform scale-105"
                    : "text-gray-700 hover:bg-white hover:text-blue-600"
                }`}
              >
                {page}
              </button>
            ))}

            <span className="px-2 text-gray-400 font-medium">...</span>

            <button
              onClick={() =>
                setCurrentPage((prev) => Math.min(prev + 1, totalPages))
              }
              disabled={currentPage === totalPages}
              className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 rounded-lg hover:bg-white disabled:hover:bg-transparent transition-all duration-200 hover:shadow-md"
            >
              <svg
                className="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(-5x);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        .animate-fadeIn {
          animation: fadeIn 0.3s ease-out;
        }
      `}</style>
    </div>
  );
}
