'use client'

import Header from "@/components/common/Header"
import Filters from "@/components/listing/Filters"
import StatsCards from "@/components/listing/StatsCards"
import { useState } from "react";
import ClientTable from "@/components/listing/ClientTable";

export default function ListingPage() {
  const [searchTerm, setSearchTerm] = useState("");
    const [statusFilter, setStatusFilter] = useState("All Status");
  return (
    <div className="min-h-screen bg-gray-100">
      <Header />
      <main className="p-3">
        <Filters searchTerm={searchTerm} setSearchTerm={setSearchTerm} statusFilter={statusFilter} setStatusFilter={setStatusFilter} />
        <StatsCards />
        <ClientTable searchTerm={searchTerm}  statusFilter={statusFilter}/>
      </main>
    </div>
  )
}
