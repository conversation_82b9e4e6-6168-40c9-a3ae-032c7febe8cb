import React, { useState, useRef, useEffect } from "react";
import { Button } from "../ui/button";
import { Plus } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from "../ui/dropdown-menu";

export default function Filters({
  searchTerm,
  setSearchTerm,
  statusFilter,
  setStatusFilter,
}) { 
  return (
    <div className="flex flex-col md:flex-row md:items-center md:justify-between w-full mb-3">
      {/* Left: Title and Subtitle */}
      <div className="mb-4 md:mb-0">
        <h2 className="text-xl md:text-2xl font-bold text-[#23235F] mb-1">
          Client Dashboard
        </h2>
        <p className="text-gray-400 text-sm md:text-base">
          Manage all your clients and their bookkeeping data
        </p>
      </div>

      {/* Center: Search and Status */}
      <div className="flex-1 flex items-center justify-center space-x-4">
        <div className="relative w-full max-w-md">
          <svg
            className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-[#7C7C9A]"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
          <input
            type="text"
            placeholder="Search by client name or email..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-3 rounded-xl border-none shadow-md bg-white text-[#7C7C9A] placeholder-[#7C7C9A] focus:outline-none focus:ring-2 focus:ring-[#6C63FF] transition-all duration-200"
          />
        </div>
        <div className="flex items-center space-x-4">
          <DropdownMenu
            selected={statusFilter}
            onSelect={setStatusFilter}
            options={["All Status", "Active", "Inactive"]}
          />
        </div>
      </div>

      {/* Right: Add New Client */}
      <div className="flex items-center mt-4 md:mt-0">
        <Button leftIcon={<Plus />}>Add New Client</Button>
      </div>
    </div>
  );
}
