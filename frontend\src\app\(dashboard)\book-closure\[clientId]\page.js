"use client";

import { useParams } from "next/navigation";
import Header from "@/components/common/Header";
import SubmitBookkeeping from "@/components/bookkeeping/SubmitBookkeeping";

export default function BookkeepingPage() {
  const params = useParams();
  const clientId = params?.clientId;
  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <SubmitBookkeeping clientId={clientId} />
    </div>
  );
}
