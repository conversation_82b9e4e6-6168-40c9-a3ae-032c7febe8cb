'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { useAuth } from '@/hooks/useAuth'
import { useToast } from '@/components/ui/toast'
import { Loader } from '@/components/ui/loading'
import '../auth.css'

export default function LoginPage() {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  })
  const [showPassword, setShowPassword] = useState(false)
  const [isLoggingIn, setIsLoggingIn] = useState(false)
  const { login, loading, error, clearError } = useAuth()
  const { addToast } = useToast()

  const handleSubmit = async (e) => {
    e.preventDefault()
    clearError()
    setIsLoggingIn(true)
    
    // Basic form validation
    if (!formData.email || !formData.password) {
      addToast('Please fill in all required fields', 'error')
      setIsLoggingIn(false)
      return
    }
    
    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(formData.email)) {
      addToast('Please enter a valid email address', 'error')
      setIsLoggingIn(false)
      return
    }
    
    const result = await login({
      email: formData.email,
      password: formData.password
    })
    
    if (!result.success) {
      addToast(result.error, 'error')
      setIsLoggingIn(false)
    }
    // If successful, the login function will handle redirect and loading state
  }

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    })
  }

  // Show loader while logging in
  if (loading || isLoggingIn) {
    return <Loader text="Signing in..." />
  }

  return (
    <div className="min-h-screen flex auth-layout">
      {/* Left Column */}
      <div className="left-column">
        <div className="text-white">
          <div className="flex items-center mb-2">
            <Image 
              src="/logo.svg" 
              alt="BG ADVISORS CPA, LTD." 
              width={48}
              height={48}
              className="h-8 sm:h-10 md:h-12 w-auto"
            />
          </div>
          <h2 className="text-xs sm:text-sm font-medium text-white uppercase tracking-wide">ADVISORS CPA, LTD.</h2>
        </div>
        
        <div className="text-white text-xs sm:text-sm opacity-60">
          Tagline
        </div>
        
        {/* Frame Element */}
        <div className="frame-element">
          <Image 
            src="/Frame.png" 
            alt="Frame" 
            width={379}
            height={365}
            className="w-full h-full object-contain"
            style={{
              overflow: 'hidden',
              height: '365px',
              width: '379px',
              objectFit: 'contain'
            }}
          />
        </div>
      </div>

      {/* Right Column */}
      <div className="right-column bg-white">

        {/* Header outside form container */}
        <div className="text-center mb-6 sm:mb-8">
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">Login</h1>
          <p className="mt-2 text-sm sm:text-base text-gray-600">Sign in to your CPA dashboard</p>
        </div>

        {/* Form Container */}
        <div className="bg-white rounded-lg shadow-xl form-container w-full max-w-md">

          
          <div className="login-form">
            {/* Email / Username Field */}
            <div className="mb-6 w-full">
              <label htmlFor="email" className="block text-sm font-medium mb-2 text-gray-700">
                Email / Username
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                  </svg>
                </div>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter your email or username"
                />
              </div>
            </div>

            {/* Password Field */}
            <div className="mb-6 w-full">
              <label htmlFor="password" className="block text-sm font-medium mb-2 text-gray-700">
                Password
              </label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  required
                  className="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter your password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                >
                  <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    {showPassword ? (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                    ) : (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    )}
                  </svg>
                </button>
              </div>
            </div>

            {/* Remember Me and Forgot Password */}
            <div className="flex items-center justify-between mb-6 w-full">
              <div className="flex items-center">
                <input
                  id="rememberMe"
                  name="rememberMe"
                  type="checkbox"
                  checked={formData.rememberMe}
                  onChange={handleChange}
                  className="h-4 w-4 rounded text-blue-600 border-gray-300"
                />
                <label htmlFor="rememberMe" className="ml-2 block text-sm text-gray-700">
                  Remember me
                </label>
              </div>
              <Link href="/forgot-password" className="text-sm text-blue-600 hover:text-blue-500">
                Forgot your password?
              </Link>
            </div>

            {/* Login Button */}
            <button
              onClick={handleSubmit}
              disabled={loading || isLoggingIn}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 mb-6"
            >
              {loading || isLoggingIn ? 'Signing In...' : 'Log In'}
            </button>

            {/* Support Contact */}
            <div className="text-center mb-2 w-full">
              <p className="text-sm text-gray-600">
                Need help? Contact support at{' '}
                <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-500">
                  <EMAIL>
                </a>
              </p>
            </div>

            {/* SSL Encryption */}
            <div className="flex items-center justify-center text-xs mb-4 w-full text-gray-500">
              <svg className="h-4 w-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              Secure login protected by SSL encryption
            </div>

            {/* Sign Up Link */}
            {/* <div className="pt-4 border-t border-gray-200 w-full">
              <p className="text-sm text-center text-gray-600">
                Don&apos;t have an account?{' '}
                <Link href="/signup" className="text-blue-600 hover:text-blue-500">
                  Sign up
                </Link>
              </p>
            </div> */}
          </div>
        </div>
      </div>
    </div>
  )
} 