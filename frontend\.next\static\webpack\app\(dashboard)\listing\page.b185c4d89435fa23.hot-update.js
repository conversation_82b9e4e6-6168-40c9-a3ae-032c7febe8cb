"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(dashboard)/listing/page",{

/***/ "(app-pages-browser)/./src/components/listing/ClientTable.jsx":
/*!************************************************!*\
  !*** ./src/components/listing/ClientTable.jsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ui/button */ \"(app-pages-browser)/./src/components/ui/button.jsx\");\n/* harmony import */ var _barrel_optimize_names_Timer_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Timer!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/timer.js\");\n/* harmony import */ var _data_clients__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/data/clients */ \"(app-pages-browser)/./src/data/clients.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ClientTable(param) {\n    let { searchTerm = \"\", statusFilter = \"All Status\" } = param;\n    _s();\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [hoveredRow, setHoveredRow] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [sortConfig, setSortConfig] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        key: null,\n        direction: \"asc\"\n    });\n    const itemsPerPage = 6;\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const filteredClients = _data_clients__WEBPACK_IMPORTED_MODULE_5__.clientsData.filter((client)=>{\n        const matchesSearch = client.name.toLowerCase().includes(searchTerm.toLowerCase()) || client.email.toLowerCase().includes(searchTerm.toLowerCase()) || client.company.toLowerCase().includes(searchTerm.toLowerCase());\n        const matchesStatus = statusFilter === \"All Status\" || client.status === statusFilter;\n        return matchesSearch && matchesStatus;\n    });\n    // Sort functionality\n    const sortedClients = [\n        ...filteredClients\n    ].sort((a, b)=>{\n        if (!sortConfig.key) return 0;\n        const aVal = a[sortConfig.key].toLowerCase();\n        const bVal = b[sortConfig.key].toLowerCase();\n        if (sortConfig.direction === \"asc\") {\n            return aVal < bVal ? -1 : aVal > bVal ? 1 : 0;\n        } else {\n            return aVal > bVal ? -1 : aVal < bVal ? 1 : 0;\n        }\n    });\n    const totalPages = Math.ceil(sortedClients.length / itemsPerPage);\n    const startIndex = (currentPage - 1) * itemsPerPage;\n    const paginatedClients = sortedClients.slice(startIndex, startIndex + itemsPerPage);\n    const handleSort = (key)=>{\n        setSortConfig((prev)=>({\n                key,\n                direction: prev.key === key && prev.direction === \"asc\" ? \"desc\" : \"asc\"\n            }));\n    };\n    const getSortIcon = (columnKey)=>{\n        if (sortConfig.key !== columnKey) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4 ml-1 text-gray-400\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M8 9l4-4 4 4m0 6l-4 4-4-4\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                    lineNumber: 68,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, this);\n        }\n        return sortConfig.direction === \"asc\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-4 h-4 ml-1 text-blue-600\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M5 15l7-7 7 7\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                lineNumber: 85,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            className: \"w-4 h-4 ml-1 text-blue-600\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M19 9l-7 7-7-7\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, this);\n    };\n    const getStatusBadge = (status)=>{\n        const baseClasses = \"px-3 py-1.5 rounded-full text-xs font-semibold transition-all duration-200 transform hover:scale-105\";\n        if (status === \"Active\") {\n            return \"\".concat(baseClasses, \" bg-green-100 text-green-700 border border-green-300\");\n        }\n        return \"\".concat(baseClasses, \" bg-red-100 text-red-700 border border-red-300\");\n    };\n    const handleViewDetails = (clientId)=>{\n        router.push(\"/dashboard\");\n    };\n    const handleBookkeeping = (clientId)=>{\n        router.push(\"/book-closure/\".concat(clientId));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-43245a1ef59431e6\" + \" \" + \"bg-white rounded-xl shadow-lg border border-gray-100 overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-43245a1ef59431e6\" + \" \" + \"px-4 py-3 border-b border-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-43245a1ef59431e6\" + \" \" + \"flex items-center justify-between\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-43245a1ef59431e6\" + \" \" + \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-43245a1ef59431e6\" + \" \" + \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-43245a1ef59431e6\" + \" \" + \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-43245a1ef59431e6\" + \" \" + \"text-gray-900 font-semibold text-lg\",\n                                        children: [\n                                            filteredClients.length,\n                                            \" clients\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-43245a1ef59431e6\" + \" \" + \"flex bg-green-100 items-center space-x-2 text-green-600 text-md font-medium p-1 rounded-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Timer_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Updated 14 minutes ago\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-43245a1ef59431e6\" + \" \" + \"overflow-x-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                    className: \"jsx-43245a1ef59431e6\" + \" \" + \"w-full table-fixed align-middle\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"colgroup\", {\n                            className: \"jsx-43245a1ef59431e6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                    style: {\n                                        width: \"25%\"\n                                    },\n                                    className: \"jsx-43245a1ef59431e6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                    style: {\n                                        width: \"20%\"\n                                    },\n                                    className: \"jsx-43245a1ef59431e6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                    style: {\n                                        width: \"15%\"\n                                    },\n                                    className: \"jsx-43245a1ef59431e6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                    style: {\n                                        width: \"15%\"\n                                    },\n                                    className: \"jsx-43245a1ef59431e6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                    lineNumber: 153,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"col\", {\n                                    style: {\n                                        width: \"20%\"\n                                    },\n                                    className: \"jsx-43245a1ef59431e6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                            lineNumber: 149,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                            className: \"jsx-43245a1ef59431e6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"jsx-43245a1ef59431e6\" + \" \" + \"border-b border-gray-200 bg-gray-100\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        onClick: ()=>handleSort(\"name\"),\n                                        className: \"jsx-43245a1ef59431e6\" + \" \" + \"px-4 py-4 text-left text-md font-semibold text-gray-700 cursor-pointer hover:text-blue-600 transition-colors duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-43245a1ef59431e6\" + \" \" + \"flex items-center\",\n                                            children: [\n                                                \"Client Name\",\n                                                getSortIcon(\"name\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        onClick: ()=>handleSort(\"company\"),\n                                        className: \"jsx-43245a1ef59431e6\" + \" \" + \"px-4 py-4 text-left text-md font-semibold text-gray-700 cursor-pointer hover:text-blue-600 transition-colors duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-43245a1ef59431e6\" + \" \" + \"flex items-center\",\n                                            children: [\n                                                \"Company Name\",\n                                                getSortIcon(\"company\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"jsx-43245a1ef59431e6\" + \" \" + \"px-2 py-4 text-left text-md font-semibold text-gray-700\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        onClick: ()=>handleSort(\"lastBookkeeping\"),\n                                        className: \"jsx-43245a1ef59431e6\" + \" \" + \"px-4 py-4 text-left text-md font-semibold text-gray-700 cursor-pointer hover:text-blue-600 transition-colors duration-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-43245a1ef59431e6\" + \" \" + \"flex items-center\",\n                                            children: [\n                                                \"Last Booking\",\n                                                getSortIcon(\"lastBookkeeping\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                        className: \"jsx-43245a1ef59431e6\" + \" \" + \"px-4 py-4 text-left text-md font-semibold text-gray-700\",\n                                        children: \"Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                lineNumber: 157,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                            lineNumber: 156,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                            className: \"jsx-43245a1ef59431e6\",\n                            children: paginatedClients.map((client, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                    onMouseEnter: ()=>setHoveredRow(client.id),\n                                    onMouseLeave: ()=>setHoveredRow(null),\n                                    className: \"jsx-43245a1ef59431e6\" + \" \" + \"transition-all duration-200 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:shadow-sm \".concat(index !== paginatedClients.length - 1 ? \"border-b border-gray-50\" : \"\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"jsx-43245a1ef59431e6\" + \" \" + \"px-3 py-2 min-w-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-43245a1ef59431e6\" + \" \" + \"flex items-center group min-w-0\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-43245a1ef59431e6\" + \" \" + \"w-12 h-12 bg-gradient-to-br from-blue-400 to-purple-600 rounded-xl flex items-center justify-center transition-all duration-300 \".concat(hoveredRow === client.id ? \"transform scale-110 shadow-lg\" : \"\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-43245a1ef59431e6\" + \" \" + \"text-white text-md font-bold\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_6__.getInitials)(client.name)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-43245a1ef59431e6\" + \" \" + \"ml-4 min-w-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-43245a1ef59431e6\" + \" \" + \"text-md font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200 truncate\",\n                                                                children: client.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                                                lineNumber: 219,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-43245a1ef59431e6\" + \" \" + \"text-md text-gray-500 group-hover:text-gray-700 transition-colors duration-200 truncate\",\n                                                                children: client.email\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                                                lineNumber: 222,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"jsx-43245a1ef59431e6\" + \" \" + \"px-3 py-2 min-w-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-43245a1ef59431e6\" + \" \" + \"text-md font-semibold text-green-900\",\n                                                children: client.company\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                                lineNumber: 229,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                            lineNumber: 228,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"jsx-43245a1ef59431e6\" + \" \" + \"px-2 py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"jsx-43245a1ef59431e6\" + \" \" + (getStatusBadge(client.status) || \"\"),\n                                                children: client.status\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"jsx-43245a1ef59431e6\" + \" \" + \"px-3 py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-43245a1ef59431e6\" + \" \" + \"text-md text-gray-900 font-medium\",\n                                                children: new Date(client.lastBookkeeping).toLocaleDateString(\"en-US\", {\n                                                    month: \"short\",\n                                                    day: \"numeric\",\n                                                    year: \"numeric\"\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                            lineNumber: 238,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                            className: \"jsx-43245a1ef59431e6\" + \" \" + \"px-3 py-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-43245a1ef59431e6\" + \" \" + \"flex items-center space-x-2 transition-all duration-300 \".concat(hoveredRow === client.id ? \"transform translate-x-1\" : \"\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        className: \"py-2 px-3\",\n                                                        onClick: ()=>handleViewDetails(client.id),\n                                                        children: \"View Details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        className: \"py-2 px-3\",\n                                                        onClick: ()=>handleBookkeeping(client.id),\n                                                        children: \"Submit Book-closure\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, client.id, true, {\n                                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                            lineNumber: 193,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                    lineNumber: 148,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                lineNumber: 147,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-43245a1ef59431e6\" + \" \" + \"px-4 py-2 border-t border-gray-100 bg-gradient-to-r from-gray-50 to-gray-100\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"jsx-43245a1ef59431e6\" + \" \" + \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-43245a1ef59431e6\" + \" \" + \"text-md text-gray-600 font-medium\",\n                            children: [\n                                \"Showing \",\n                                startIndex + 1,\n                                \" to\",\n                                \" \",\n                                Math.min(startIndex + itemsPerPage, filteredClients.length),\n                                \" of\",\n                                \" \",\n                                filteredClients.length,\n                                \" results\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-43245a1ef59431e6\" + \" \" + \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentPage((prev)=>Math.max(prev - 1, 1)),\n                                    disabled: currentPage === 1,\n                                    className: \"jsx-43245a1ef59431e6\" + \" \" + \"p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 rounded-lg hover:bg-white disabled:hover:bg-transparent transition-all duration-200 hover:shadow-md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        className: \"jsx-43245a1ef59431e6\" + \" \" + \"w-5 h-5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M15 19l-7-7 7-7\",\n                                            className: \"jsx-43245a1ef59431e6\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                            lineNumber: 297,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 13\n                                }, this),\n                                [\n                                    1,\n                                    2,\n                                    3\n                                ].map((page)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setCurrentPage(page),\n                                        className: \"jsx-43245a1ef59431e6\" + \" \" + \"px-4 py-2 rounded-lg text-md font-semibold transition-all duration-200 hover:shadow-md \".concat(currentPage === page ? \"bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-lg transform scale-105\" : \"text-gray-700 hover:bg-white hover:text-blue-600\"),\n                                        children: page\n                                    }, page, false, {\n                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 15\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"jsx-43245a1ef59431e6\" + \" \" + \"px-2 text-gray-400 font-medium\",\n                                    children: \"...\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setCurrentPage((prev)=>Math.min(prev + 1, totalPages)),\n                                    disabled: currentPage === totalPages,\n                                    className: \"jsx-43245a1ef59431e6\" + \" \" + \"p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 rounded-lg hover:bg-white disabled:hover:bg-transparent transition-all duration-200 hover:shadow-md\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        className: \"jsx-43245a1ef59431e6\" + \" \" + \"w-5 h-5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M9 5l7 7-7 7\",\n                                            className: \"jsx-43245a1ef59431e6\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                    lineNumber: 279,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n                lineNumber: 278,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"43245a1ef59431e6\",\n                children: \"@keyframes fadeIn{from{opacity:0;transform:translatey(-5x)}to{opacity:1;transform:translatey(0)}}.animate-fadeIn.jsx-43245a1ef59431e6{animation:fadeIn.3s ease-out}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Projects\\\\Cpa-dashboard\\\\cpa-dashboard\\\\cpa-dashboard\\\\frontend\\\\src\\\\components\\\\listing\\\\ClientTable.jsx\",\n        lineNumber: 127,\n        columnNumber: 5\n    }, this);\n}\n_s(ClientTable, \"cuvYyHjphjJq2wsioTVZk4v5XdU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = ClientTable;\nvar _c;\n$RefreshReg$(_c, \"ClientTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/listing/ClientTable.jsx\n"));

/***/ })

});