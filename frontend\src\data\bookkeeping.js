import { Receipt, RefreshCcw, Users } from "lucide-react";

export const cardConfigs = [
  {
    key: "financial",
    title: "Financial Data",
    description: "Upload bank statements, invoices, receipts",
    icon: (
      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto">
        <Receipt />
      </div>
    ),
    lastSync: "08/04/2024",
  },
  {
    key: "operational",
    title: "Operational Data",
    description: "Sync operational reports, metrics",
    icon: (
      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto">
        <RefreshCcw />
      </div>
    ),
    lastSync: "12/05/2024",
  },
  {
    key: "payroll",
    title: "ADP/Payroll Data",
    description: "Upload payroll reports, ADP data",
    icon: (
      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto">
        <Users />
      </div>
    ),
    lastSync: "24/02/2024",
  },
];

export const bookkeepingMonths = [
  "January 2024",
  "February 2024",
  "March 2024",
  "April 2024",
  "May 2024",
  "June 2024",
  "July 2024",
  "August 2024",
  "September 2024",
  "October 2024",
  "November 2024",
  "December 2024",
];
