{"level":"info","message":"Auth0 strategy configured successfully","timestamp":"2025-08-05 10:22:57"}
{"level":"info","message":"Auth0 SSO strategy configured successfully","timestamp":"2025-08-05 10:22:57"}
{"level":"info","message":"Connected to database","timestamp":"2025-08-05 10:23:02"}
{"level":"info","message":"Database tables synchronized successfully","timestamp":"2025-08-05 10:23:18"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 10:24:08"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 10:24:08"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 10:24:08"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 10:24:08"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 10:24:10"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 10:24:11"}
{"level":"info","timestamp":"2025-08-05 10:24:11"}
{"level":"info","message":"::1 - - [05/Aug/2025:04:54:11 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 10:24:11"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 10:25:40"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 10:25:40"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 10:25:40"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 10:25:43"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 10:25:43"}
{"level":"info","timestamp":"2025-08-05 10:25:43"}
{"level":"info","message":"::1 - - [05/Aug/2025:04:55:43 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 10:25:43"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 10:29:54"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 10:29:54"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 10:29:54"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 10:29:57"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 10:29:57"}
{"level":"info","timestamp":"2025-08-05 10:29:57"}
{"level":"info","message":"::1 - - [05/Aug/2025:04:59:57 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 10:29:57"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 10:33:12"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 10:33:12"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 10:33:12"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 10:33:19"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 10:33:20"}
{"level":"info","timestamp":"2025-08-05 10:33:20"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:03:20 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 10:33:20"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 10:33:37"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 10:33:37"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 10:33:37"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 10:33:42"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 10:33:43"}
{"level":"info","timestamp":"2025-08-05 10:33:43"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:03:43 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 10:33:43"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 10:34:37"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 10:34:37"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 10:34:37"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 10:34:40"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 10:34:41"}
{"level":"info","timestamp":"2025-08-05 10:34:41"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:04:41 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 10:34:41"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 10:37:04"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 10:37:04"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 10:37:04"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 10:37:07"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 10:37:07"}
{"level":"info","timestamp":"2025-08-05 10:37:07"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:07:07 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 10:37:07"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 10:38:17"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 10:38:17"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 10:38:17"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 10:38:19"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 10:38:19"}
{"level":"info","timestamp":"2025-08-05 10:38:19"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:08:19 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 10:38:19"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 10:38:33"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 10:38:33"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 10:38:33"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 10:38:36"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 10:38:36"}
{"level":"info","timestamp":"2025-08-05 10:38:36"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:08:36 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 10:38:36"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 10:40:52"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 10:40:52"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 10:40:52"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 10:40:54"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 10:40:55"}
{"level":"info","timestamp":"2025-08-05 10:40:55"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:10:55 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 10:40:55"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 10:44:13"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 10:44:13"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 10:44:13"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 10:44:16"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 10:44:16"}
{"level":"info","timestamp":"2025-08-05 10:44:16"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:14:16 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 10:44:16"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 10:45:40"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 10:45:40"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 10:45:40"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 10:45:43"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 10:45:43"}
{"level":"info","timestamp":"2025-08-05 10:45:43"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:15:43 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 10:45:43"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 10:46:21"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 10:46:21"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 10:46:21"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 10:46:23"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 10:46:23"}
{"level":"info","timestamp":"2025-08-05 10:46:23"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:16:23 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 10:46:23"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 10:47:00"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 10:47:00"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 10:47:00"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 10:47:03"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 10:47:03"}
{"level":"info","timestamp":"2025-08-05 10:47:03"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:17:03 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 10:47:03"}
{"level":"info","message":"Auth0 strategy configured successfully","timestamp":"2025-08-05 10:53:39"}
{"level":"info","message":"Auth0 SSO strategy configured successfully","timestamp":"2025-08-05 10:53:39"}
{"level":"info","message":"Connected to database","timestamp":"2025-08-05 10:53:45"}
{"level":"info","message":"Database tables synchronized successfully","timestamp":"2025-08-05 10:54:00"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 10:54:20"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 10:54:20"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 10:54:20"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 10:54:23"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 10:54:23"}
{"level":"info","timestamp":"2025-08-05 10:54:23"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:24:23 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 10:54:23"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 10:55:00"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 10:55:00"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 10:55:00"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 10:55:03"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 10:55:03"}
{"level":"info","timestamp":"2025-08-05 10:55:03"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:25:03 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 10:55:03"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 11:06:41"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 11:06:41"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 11:06:41"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 11:06:46"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 11:06:46"}
{"level":"info","timestamp":"2025-08-05 11:06:46"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:36:46 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 11:06:46"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 11:08:45"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 11:08:45"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 11:08:45"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 11:08:47"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 11:08:48"}
{"level":"info","timestamp":"2025-08-05 11:08:48"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:38:48 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 11:08:48"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 11:10:09"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 11:10:09"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 11:10:09"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 11:10:12"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 11:10:12"}
{"level":"info","timestamp":"2025-08-05 11:10:12"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:40:12 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 11:10:12"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 11:10:46"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 11:10:46"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 11:10:46"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 11:10:49"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 11:10:49"}
{"level":"info","timestamp":"2025-08-05 11:10:49"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:40:49 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 11:10:49"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 11:13:29"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 11:13:29"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 11:13:29"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 11:13:32"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 11:13:32"}
{"level":"info","timestamp":"2025-08-05 11:13:32"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:43:32 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 11:13:32"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 11:16:13"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 11:16:13"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 11:16:13"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 11:16:15"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 11:16:16"}
{"level":"info","timestamp":"2025-08-05 11:16:16"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:46:16 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 11:16:16"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 11:17:17"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 11:17:17"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 11:17:17"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 11:17:20"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 11:17:20"}
{"level":"info","timestamp":"2025-08-05 11:17:20"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:47:20 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 11:17:20"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 11:18:53"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 11:18:53"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 11:18:53"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 11:18:56"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 11:18:56"}
{"level":"info","timestamp":"2025-08-05 11:18:56"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:48:56 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 11:18:56"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 11:19:00"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 11:19:00"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 11:19:00"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 11:19:01"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 11:19:01"}
{"level":"info","timestamp":"2025-08-05 11:19:01"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:49:01 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 11:19:01"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 11:19:24"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 11:19:24"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:49:24 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 400 313 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 11:19:24"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 11:21:27"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 11:21:27"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:51:27 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 400 313 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 11:21:27"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 11:22:01"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 11:22:01"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:52:01 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 400 313 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 11:22:01"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 11:22:05"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 11:22:05"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 11:22:05"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 11:22:08"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 11:22:08"}
{"level":"info","timestamp":"2025-08-05 11:22:08"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:52:08 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 11:22:08"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 11:22:13"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 11:22:13"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:52:13 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 400 313 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 11:22:13"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 11:22:19"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 11:22:19"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:52:19 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 400 313 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 11:22:19"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 11:22:22"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 11:22:22"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:52:22 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 400 313 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 11:22:22"}
{"level":"info","message":"Auth0 strategy configured successfully","timestamp":"2025-08-05 11:23:34"}
{"level":"info","message":"Auth0 SSO strategy configured successfully","timestamp":"2025-08-05 11:23:34"}
{"level":"info","message":"Connected to database","timestamp":"2025-08-05 11:23:40"}
{"level":"info","message":"Database tables synchronized successfully","timestamp":"2025-08-05 11:23:55"}
{"level":"info","message":"Auth0 strategy configured successfully","timestamp":"2025-08-05 11:24:46"}
{"level":"info","message":"Auth0 SSO strategy configured successfully","timestamp":"2025-08-05 11:24:46"}
{"level":"info","message":"Connected to database","timestamp":"2025-08-05 11:24:52"}
{"level":"info","message":"Database tables synchronized successfully","timestamp":"2025-08-05 11:25:07"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 11:26:09"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 11:26:09"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 11:26:09"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 11:26:12"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 11:26:12"}
{"level":"info","timestamp":"2025-08-05 11:26:12"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:56:12 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 11:26:12"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 11:26:28"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 11:26:28"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 11:26:28"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 11:26:30"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 11:26:30"}
{"level":"info","timestamp":"2025-08-05 11:26:30"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:56:30 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 11:26:30"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 11:26:48"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 11:26:48"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:56:48 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 400 313 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 11:26:48"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 11:26:51"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 11:26:51"}
{"level":"info","message":"::1 - - [05/Aug/2025:05:56:51 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 400 313 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 11:26:51"}
{"level":"info","message":"Auth0 strategy configured successfully","timestamp":"2025-08-05 11:47:24"}
{"level":"info","message":"Auth0 SSO strategy configured successfully","timestamp":"2025-08-05 11:47:24"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 11:49:20"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 11:49:20"}
{"level":"info","message":"::1 - - [05/Aug/2025:06:19:20 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 400 313 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 11:49:20"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 11:49:21"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 11:49:21"}
{"level":"info","message":"::1 - - [05/Aug/2025:06:19:21 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 400 313 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 11:49:21"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 11:49:24"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 11:49:24"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 11:49:24"}
{"level":"warn","message":"Invalid Credentials","timestamp":"2025-08-05 11:49:26"}
{"level":"info","message":"::1 - - [05/Aug/2025:06:19:26 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 400 54 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 11:49:26"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 11:49:32"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 11:49:32"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 11:49:32"}
{"level":"warn","message":"Invalid Credentials","timestamp":"2025-08-05 11:49:32"}
{"level":"info","message":"::1 - - [05/Aug/2025:06:19:32 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 400 54 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 11:49:32"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 11:49:40"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 11:49:40"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 11:49:40"}
{"level":"info","message":"Access token created for user ID: 4","timestamp":"2025-08-05 11:49:40"}
{"level":"info","message":"Refresh token created for user ID: 4","timestamp":"2025-08-05 11:49:40"}
{"level":"info","timestamp":"2025-08-05 11:49:40"}
{"level":"info","message":"::1 - - [05/Aug/2025:06:19:40 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 866 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 11:49:40"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 12:09:26"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 12:09:26"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 12:09:26"}
{"level":"warn","message":"Invalid Credentials","timestamp":"2025-08-05 12:09:28"}
{"level":"info","message":"::1 - - [05/Aug/2025:06:39:28 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 400 54 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 12:09:28"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 12:09:50"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 12:09:50"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 12:09:50"}
{"level":"warn","message":"Invalid Credentials","timestamp":"2025-08-05 12:09:53"}
{"level":"info","message":"::1 - - [05/Aug/2025:06:39:53 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 400 54 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 12:09:53"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 12:10:22"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 12:10:22"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 12:10:22"}
{"level":"warn","message":"Invalid Credentials","timestamp":"2025-08-05 12:10:25"}
{"level":"info","message":"::1 - - [05/Aug/2025:06:40:25 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 400 54 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 12:10:25"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 12:10:35"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 12:10:35"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 12:10:35"}
{"level":"info","message":"Access token created for user ID: 6","timestamp":"2025-08-05 12:10:35"}
{"level":"info","message":"Refresh token created for user ID: 6","timestamp":"2025-08-05 12:10:36"}
{"level":"info","timestamp":"2025-08-05 12:10:36"}
{"level":"info","message":"::1 - - [05/Aug/2025:06:40:36 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 845 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 12:10:36"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 12:11:16"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 12:11:16"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 12:11:16"}
{"level":"warn","message":"Invalid Credentials","timestamp":"2025-08-05 12:11:19"}
{"level":"info","message":"::1 - - [05/Aug/2025:06:41:19 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 400 54 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 12:11:19"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 12:12:37"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 12:12:37"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 12:12:37"}
{"level":"warn","message":"Invalid Credentials","timestamp":"2025-08-05 12:12:40"}
{"level":"info","message":"::1 - - [05/Aug/2025:06:42:40 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 400 54 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 12:12:40"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 12:12:54"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 12:12:54"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 12:12:54"}
{"level":"info","message":"Access token created for user ID: 8","timestamp":"2025-08-05 12:12:56"}
{"level":"info","message":"Refresh token created for user ID: 8","timestamp":"2025-08-05 12:12:57"}
{"level":"info","timestamp":"2025-08-05 12:12:57"}
{"level":"info","message":"::1 - - [05/Aug/2025:06:42:57 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 821 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 12:12:57"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 12:15:09"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 12:15:09"}
{"level":"info","message":"::1 - - [05/Aug/2025:06:45:09 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 400 313 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 12:15:09"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 12:15:21"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 12:15:21"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 12:15:21"}
{"level":"info","message":"Access token created for user ID: 8","timestamp":"2025-08-05 12:15:24"}
{"level":"info","message":"Refresh token created for user ID: 8","timestamp":"2025-08-05 12:15:24"}
{"level":"info","timestamp":"2025-08-05 12:15:24"}
{"level":"info","message":"::1 - - [05/Aug/2025:06:45:24 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 821 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 12:15:24"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 12:24:11"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 12:24:11"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 12:24:11"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 12:24:11"}
{"level":"info","message":"Access token created for user ID: 8","timestamp":"2025-08-05 12:24:14"}
{"level":"info","message":"Refresh token created for user ID: 8","timestamp":"2025-08-05 12:24:15"}
{"level":"info","timestamp":"2025-08-05 12:24:15"}
{"level":"info","message":"::1 - - [05/Aug/2025:06:54:15 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 821 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 12:24:15"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 12:25:17"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 12:25:17"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 12:25:17"}
{"level":"info","message":"Access token created for user ID: 7","timestamp":"2025-08-05 12:25:21"}
{"level":"info","message":"Refresh token created for user ID: 7","timestamp":"2025-08-05 12:25:21"}
{"level":"info","timestamp":"2025-08-05 12:25:21"}
{"level":"info","message":"::1 - - [05/Aug/2025:06:55:21 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 840 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 12:25:21"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 12:26:15"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 12:26:15"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 12:26:15"}
{"level":"info","message":"Access token created for user ID: 7","timestamp":"2025-08-05 12:26:17"}
{"level":"info","message":"Refresh token created for user ID: 7","timestamp":"2025-08-05 12:26:18"}
{"level":"info","timestamp":"2025-08-05 12:26:18"}
{"level":"info","message":"::1 - - [05/Aug/2025:06:56:18 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 840 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 12:26:18"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 12:26:24"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 12:26:24"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 12:26:24"}
{"level":"info","message":"Access token created for user ID: 8","timestamp":"2025-08-05 12:26:24"}
{"level":"info","message":"Refresh token created for user ID: 8","timestamp":"2025-08-05 12:26:24"}
{"level":"info","timestamp":"2025-08-05 12:26:24"}
{"level":"info","message":"::1 - - [05/Aug/2025:06:56:24 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 821 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 12:26:24"}
{"level":"info","message":"Auth0 strategy configured successfully","timestamp":"2025-08-05 13:56:33"}
{"level":"info","message":"Auth0 SSO strategy configured successfully","timestamp":"2025-08-05 13:56:33"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 13:56:35"}
{"level":"info","message":"POST /api/v1/auth/refresh-token - Origin: http://localhost:3000","timestamp":"2025-08-05 13:56:35"}
{"level":"info","timestamp":"2025-08-05 13:56:35"}
{"level":"info","message":"::1 - - [05/Aug/2025:08:26:35 +0000] \"POST /api/v1/auth/refresh-token HTTP/1.1\" 401 51 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 13:56:35"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 13:56:41"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 13:56:41"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 13:56:41"}
{"level":"info","message":"Access token created for user ID: 8","timestamp":"2025-08-05 13:56:41"}
{"level":"info","message":"Refresh token created for user ID: 8","timestamp":"2025-08-05 13:56:42"}
{"level":"info","timestamp":"2025-08-05 13:56:42"}
{"level":"info","message":"::1 - - [05/Aug/2025:08:26:42 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 821 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 13:56:42"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 14:26:13"}
{"level":"info","message":"POST /api/v1/auth/refresh-token - Origin: http://localhost:3000","timestamp":"2025-08-05 14:26:13"}
{"level":"info","timestamp":"2025-08-05 14:26:13"}
{"level":"info","message":"Fetching user by ID: 8","timestamp":"2025-08-05 14:26:16"}
{"level":"info","message":"refresh token revoked successfully","timestamp":"2025-08-05 14:26:16"}
{"level":"info","message":"Access token created for user ID: 8","timestamp":"2025-08-05 14:26:16"}
{"level":"info","message":"Refresh token created for user ID: 8","timestamp":"2025-08-05 14:26:16"}
{"level":"info","message":"::1 - - [05/Aug/2025:08:56:16 +0000] \"POST /api/v1/auth/refresh-token HTTP/1.1\" 200 495 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 14:26:16"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 14:57:27"}
{"level":"info","message":"POST /api/v1/auth/refresh-token - Origin: http://localhost:3000","timestamp":"2025-08-05 14:57:27"}
{"level":"info","timestamp":"2025-08-05 14:57:27"}
{"level":"info","message":"Fetching user by ID: 8","timestamp":"2025-08-05 14:57:29"}
{"level":"info","message":"refresh token revoked successfully","timestamp":"2025-08-05 14:57:30"}
{"level":"info","message":"Access token created for user ID: 8","timestamp":"2025-08-05 14:57:30"}
{"level":"info","message":"Refresh token created for user ID: 8","timestamp":"2025-08-05 14:57:30"}
{"level":"info","message":"::1 - - [05/Aug/2025:09:27:30 +0000] \"POST /api/v1/auth/refresh-token HTTP/1.1\" 200 495 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 14:57:30"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 15:01:24"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 15:01:24"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 15:01:24"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 15:01:24"}
{"level":"info","message":"Access token created for user ID: 8","timestamp":"2025-08-05 15:01:26"}
{"level":"info","message":"Refresh token created for user ID: 8","timestamp":"2025-08-05 15:01:27"}
{"level":"info","timestamp":"2025-08-05 15:01:27"}
{"level":"info","message":"::1 - - [05/Aug/2025:09:31:27 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 821 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 15:01:27"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 15:26:44"}
{"level":"info","message":"POST /api/v1/auth/refresh-token - Origin: http://localhost:3000","timestamp":"2025-08-05 15:26:44"}
{"level":"info","timestamp":"2025-08-05 15:26:45"}
{"level":"info","message":"Fetching user by ID: 8","timestamp":"2025-08-05 15:26:47"}
{"level":"info","message":"refresh token revoked successfully","timestamp":"2025-08-05 15:26:48"}
{"level":"info","message":"Access token created for user ID: 8","timestamp":"2025-08-05 15:26:48"}
{"level":"info","message":"Refresh token created for user ID: 8","timestamp":"2025-08-05 15:26:48"}
{"level":"info","message":"::1 - - [05/Aug/2025:09:56:48 +0000] \"POST /api/v1/auth/refresh-token HTTP/1.1\" 200 495 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 15:26:48"}
{"level":"info","message":"Auth0 strategy configured successfully","timestamp":"2025-08-05 15:34:42"}
{"level":"info","message":"Auth0 SSO strategy configured successfully","timestamp":"2025-08-05 15:34:42"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 15:52:42"}
{"level":"info","message":"POST /api/v1/auth/refresh-token - Origin: http://localhost:3000","timestamp":"2025-08-05 15:52:42"}
{"level":"info","timestamp":"2025-08-05 15:52:42"}
{"level":"info","message":"Fetching user by ID: 8","timestamp":"2025-08-05 15:52:44"}
{"level":"info","message":"refresh token revoked successfully","timestamp":"2025-08-05 15:52:45"}
{"level":"info","message":"Access token created for user ID: 8","timestamp":"2025-08-05 15:52:45"}
{"level":"info","message":"Refresh token created for user ID: 8","timestamp":"2025-08-05 15:52:45"}
{"level":"info","message":"::1 - - [05/Aug/2025:10:22:45 +0000] \"POST /api/v1/auth/refresh-token HTTP/1.1\" 200 495 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 15:52:45"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 16:13:47"}
{"level":"info","message":"POST /api/v1/auth/refresh-token - Origin: http://localhost:3000","timestamp":"2025-08-05 16:13:47"}
{"level":"info","timestamp":"2025-08-05 16:13:47"}
{"level":"info","message":"Fetching user by ID: 8","timestamp":"2025-08-05 16:13:50"}
{"level":"info","message":"refresh token revoked successfully","timestamp":"2025-08-05 16:13:50"}
{"level":"info","message":"Access token created for user ID: 8","timestamp":"2025-08-05 16:13:50"}
{"level":"info","message":"Refresh token created for user ID: 8","timestamp":"2025-08-05 16:13:51"}
{"level":"info","message":"::1 - - [05/Aug/2025:10:43:51 +0000] \"POST /api/v1/auth/refresh-token HTTP/1.1\" 200 495 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 16:13:51"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 16:26:05"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 16:26:05"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 16:26:05"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 16:26:05"}
{"level":"info","message":"Access token created for user ID: 8","timestamp":"2025-08-05 16:26:08"}
{"level":"info","message":"Refresh token created for user ID: 8","timestamp":"2025-08-05 16:26:08"}
{"level":"info","timestamp":"2025-08-05 16:26:08"}
{"level":"info","message":"::1 - - [05/Aug/2025:10:56:08 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 821 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 16:26:08"}
{"level":"info","message":"Auth0 strategy configured successfully","timestamp":"2025-08-05 16:56:48"}
{"level":"info","message":"Auth0 SSO strategy configured successfully","timestamp":"2025-08-05 16:56:48"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 16:57:19"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 16:57:19"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 16:57:19"}
{"level":"info","message":"Access token created for user ID: 8","timestamp":"2025-08-05 16:57:21"}
{"level":"info","message":"Refresh token created for user ID: 8","timestamp":"2025-08-05 16:57:21"}
{"level":"info","timestamp":"2025-08-05 16:57:21"}
{"level":"info","message":"::1 - - [05/Aug/2025:11:27:21 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 821 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 16:57:21"}
{"level":"info","message":"Auth0 strategy configured successfully","timestamp":"2025-08-05 17:09:36"}
{"level":"info","message":"Auth0 SSO strategy configured successfully","timestamp":"2025-08-05 17:09:36"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 17:14:41"}
{"level":"info","message":"POST /api/v1/auth/refresh-token - Origin: http://localhost:3000","timestamp":"2025-08-05 17:14:41"}
{"level":"info","timestamp":"2025-08-05 17:14:41"}
{"level":"info","message":"Fetching user by ID: 8","timestamp":"2025-08-05 17:14:44"}
{"level":"info","message":"refresh token revoked successfully","timestamp":"2025-08-05 17:14:44"}
{"level":"info","message":"Access token created for user ID: 8","timestamp":"2025-08-05 17:14:44"}
{"level":"info","message":"Refresh token created for user ID: 8","timestamp":"2025-08-05 17:14:44"}
{"level":"info","message":"::1 - - [05/Aug/2025:11:44:44 +0000] \"POST /api/v1/auth/refresh-token HTTP/1.1\" 200 495 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 17:14:44"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 17:37:52"}
{"level":"info","message":"POST /api/v1/auth/refresh-token - Origin: http://localhost:3000","timestamp":"2025-08-05 17:37:52"}
{"level":"info","timestamp":"2025-08-05 17:37:52"}
{"level":"info","message":"Fetching user by ID: 8","timestamp":"2025-08-05 17:37:55"}
{"level":"info","message":"refresh token revoked successfully","timestamp":"2025-08-05 17:37:56"}
{"level":"info","message":"Access token created for user ID: 8","timestamp":"2025-08-05 17:37:56"}
{"level":"info","message":"Refresh token created for user ID: 8","timestamp":"2025-08-05 17:37:56"}
{"level":"info","message":"::1 - - [05/Aug/2025:12:07:56 +0000] \"POST /api/v1/auth/refresh-token HTTP/1.1\" 200 495 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 17:37:56"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 17:50:06"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 17:50:07"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 17:50:07"}
{"level":"info","message":"Access token created for user ID: 7","timestamp":"2025-08-05 17:50:09"}
{"level":"info","message":"Refresh token created for user ID: 7","timestamp":"2025-08-05 17:50:09"}
{"level":"info","timestamp":"2025-08-05 17:50:09"}
{"level":"info","message":"::1 - - [05/Aug/2025:12:20:09 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 840 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 17:50:09"}
{"level":"info","message":"CORS: Allowing origin http://localhost:3000","timestamp":"2025-08-05 17:50:54"}
{"level":"info","message":"POST /api/v1/auth/login - Origin: http://localhost:3000","timestamp":"2025-08-05 17:50:54"}
{"level":"info","message":"Logging user","timestamp":"2025-08-05 17:50:54"}
{"level":"info","message":"Access token created for user ID: 8","timestamp":"2025-08-05 17:50:56"}
{"level":"info","message":"Refresh token created for user ID: 8","timestamp":"2025-08-05 17:50:57"}
{"level":"info","timestamp":"2025-08-05 17:50:57"}
{"level":"info","message":"::1 - - [05/Aug/2025:12:20:57 +0000] \"POST /api/v1/auth/login HTTP/1.1\" 200 821 \"http://localhost:3000/\" \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"","timestamp":"2025-08-05 17:50:57"}
